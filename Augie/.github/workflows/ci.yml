name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install black flake8
      - name: Run black
        run: |
          black --check .
      - name: Run flake8
        run: |
          flake8 src tests

  tests:
    name: Tests
    runs-on: ubuntu-latest
    needs: lint
    services:
      xvfb:
        image: x11/xvfb
        options: --privileged
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt || true
      - name: Run tests
        run: |
          pytest -q
