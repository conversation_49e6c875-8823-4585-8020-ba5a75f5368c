# 🤖 Act Agent Implementation Instructions

## 🎯 **For Qwen Coder3 or Other Free Coding Models**

You are the **Act Agent** working with Claude 3.5 Son<PERSON> as the **Planner Agent**. Your job is to implement specific, focused coding tasks for The Printery AI Assistant.

---

## 🍒 **The Printery Branding - ALWAYS USE THESE COLORS**

```python
# The Printery Theme Colors (MEMORIZE THESE)
self.colors = {
    'bg_primary': '#ffffff',        # White background
    'bg_secondary': '#fef7f7',      # Very light pink tint
    'bg_tertiary': '#f0f8ff',       # Very light blue tint
    'text_primary': '#000000',      # Black text
    'text_secondary': '#4a4a4a',    # Dark gray text
    'accent': '#20B2AA',            # The Printery TEAL (main accent)
    'user_msg': '#FFB6C1',          # The Printery PINK (user messages)
    'assistant_msg': '#E0F8F8',     # Light teal (assistant messages)
    'hover': '#f5f5f5',             # Hover state
}
```

---

## 📁 **Key Files You'll Work With**

### **Main UI File**: `src/ui/modern_chat_window_v2.py`
- Contains the beautiful UI with all tabs
- Has The Printery branding already applied
- Your primary file for UI changes

### **Assistant Management**: `src/assistants/assistant_manager.py`
- Manages AI assistants/agents
- Where you'll add category functionality

### **Data Files**: `data/topics.json`
- Stores conversation topics
- JSON format for easy manipulation

---

## 🚀 **TASK 1A: Search UI Components (READY TO IMPLEMENT)**

### **Your Mission:**
Add search interface to the beautiful UI when user clicks the "🔍 Search" button.

### **Existing Code Context:**
In `src/ui/modern_chat_window_v2.py`, there's already a search button:
```python
search_btn = tk.Button(
    buttons_frame,
    text="🔍 Search",
    bg=self.colors['bg_tertiary'],
    fg=self.colors['text_primary'],
    border=0,
    font=('Arial', 9),
    cursor='hand2'
)
```

### **What You Need to Do:**

1. **Add search view to switch_view() method:**
```python
def switch_view(self, view):
    """Switch between different main views."""
    self.current_view = view
    
    # Add this case:
    if view == "search":
        self.setup_search_view()
    
    # Refresh the main content
    for widget in self.root.winfo_children():
        widget.destroy()
    self.setup_ui()
```

2. **Create setup_search_view() method:**
```python
def setup_search_view(self, parent):
    """Set up the search interface."""
    # Search header
    search_header = tk.Frame(parent, bg=self.colors['bg_primary'])
    search_header.pack(fill=tk.X, pady=20)
    
    tk.Label(
        search_header,
        text="🔍 Search Conversations",
        font=('Arial', 18, 'bold'),
        bg=self.colors['bg_primary'],
        fg=self.colors['text_primary']
    ).pack()
    
    # Search input
    search_input_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
    search_input_frame.pack(fill=tk.X, padx=50, pady=20)
    
    self.search_entry = tk.Entry(
        search_input_frame,
        font=('Arial', 14),
        bg=self.colors['bg_tertiary'],
        fg=self.colors['text_primary'],
        relief=tk.SOLID,
        bd=1
    )
    self.search_entry.pack(fill=tk.X, pady=10)
    self.search_entry.bind('<Return>', self.perform_search)
    
    # Search button
    search_btn = tk.Button(
        search_input_frame,
        text="Search",
        bg=self.colors['accent'],  # The Printery teal
        fg='white',
        font=('Arial', 12, 'bold'),
        cursor='hand2',
        command=self.perform_search
    )
    search_btn.pack(pady=10)
    
    # Results area
    self.search_results_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
    self.search_results_frame.pack(fill=tk.BOTH, expand=True, padx=50)
```

3. **Connect search button to search view:**
```python
# Update the search button command:
search_btn = tk.Button(
    buttons_frame,
    text="🔍 Search",
    bg=self.colors['bg_tertiary'],
    fg=self.colors['text_primary'],
    border=0,
    font=('Arial', 9),
    cursor='hand2',
    command=lambda: self.switch_view("search")  # ADD THIS LINE
)
```

### **Expected Result:**
- Clicking "🔍 Search" button switches to search view
- Search interface appears with The Printery teal accent color
- Search input field is ready for user input
- Results area is prepared for displaying results

### **Test Your Work:**
Run `python test_beautiful_ui_complete.py` and verify:
1. Search button works
2. Search interface appears
3. The Printery colors are used correctly
4. No errors in console

---

## 🎯 **TASK 1B: Search Logic (NEXT TASK)**

### **Your Mission:**
Implement the actual search functionality to find topics and messages.

### **What You'll Add:**
```python
def perform_search(self, event=None):
    """Perform search across topics."""
    query = self.search_entry.get().strip()
    if not query:
        return
    
    results = self.search_topics(query)
    self.display_search_results(results)

def search_topics(self, query):
    """Search through topics and messages."""
    results = []
    query_lower = query.lower()
    
    for topic in self.topics:
        # Search in topic title
        if query_lower in topic.title.lower():
            results.append({
                'type': 'topic',
                'topic': topic,
                'match': 'title',
                'text': topic.title
            })
        
        # Search in messages
        for message in topic.messages:
            if query_lower in message.content.lower():
                results.append({
                    'type': 'message',anthropic/claude-sonnet-4
                    'topic': topic,
                    'message': message,
                    'match': 'content',
                    'text': message.content[:100] + '...'
                })
    
    return results
```

---

## 💡 **Success Tips for Act Agent**

1. **Always use The Printery colors** - `self.colors['accent']` and `self.colors['user_msg']`
2. **Test immediately** after each change
3. **Keep it simple** - focus on the specific task only
4. **Copy existing patterns** from the current code
5. **Ask for help** if you need clarification on any step

---

## 🔄 **Workflow with Planner Agent**

1. **Planner (Claude)** provides detailed architecture and design
2. **Act Agent (You)** implements the specific code changes
3. **Test** the implementation
4. **Report back** on success or issues
5. **Planner** provides next task or fixes

This approach maximizes efficiency and minimizes costs while ensuring high-quality implementation of The Printery AI Assistant!

---

**Ready to start? Begin with TASK 1A: Search UI Components** 🚀
