[DEFAULT]
log_level = DEBUG
app_name = VoiceFlow AI
version = 0.1.0

[VOICE]
# Disable voice features by default
enable_voice = False
wake_word = hey assistant
energy_threshold = 4000
pause_threshold = 0.8
tts_rate = 150
tts_volume = 0.9
tts_voice = 0

# LLM Configuration
[LLM]
# Choose the LLM provider (options: openai, anthropic, gemini, ollama, lmstudio)
provider = ollama
model = phi4:latest
max_tokens = 1000
temperature = 0.7

# Ollama specific settings (only used when provider=ollama)
[OLLAMA]
base_url = http://localhost:11434
model = phi4:latest

[UI]
theme = darkly
width = 800
height = 600
