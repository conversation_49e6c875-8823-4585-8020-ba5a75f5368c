"""
Debug script for the VoiceFlow AI Assistant GUI.
"""
import asyncio
import logging
import sys
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('debug_gui.log')
    ]
)
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from src.assistant import VoiceAssistant
from src.utils.config import load_config

async def main():
    """Run the GUI with debug logging."""
    try:
        # Load configuration
        config_path = Path(__file__).parent / 'clean_config.ini'
        if not config_path.exists():
            logger.error(f"Configuration file not found: {config_path}")
            return
            
        logger.info(f"Loading configuration from: {config_path}")
        config = load_config(config_path)
        
        # Initialize the assistant
        logger.info("Initializing VoiceAssistant...")
        assistant = VoiceAssistant(config)
        await assistant.initialize()
        
        # Check which LLM provider is being used
        provider = config.get('LLM', 'provider', fallback='ollama')
        logger.info(f"Using LLM provider: {provider}")
        logger.info(f"Current LLM instance: {assistant.current_llm}")
        
        # Test a simple message
        logger.info("Sending test message to assistant...")
        response = await assistant.process_text_input("Hello, how are you?")
        logger.info(f"Assistant response: {response}")
        
        # If we got here, the basic test passed
        logger.info("Basic test passed! Now starting the GUI...")
        
        # Import and run the GUI
        from src.gui_new import main as gui_main
        await gui_main()
        
    except Exception as e:
        logger.error(f"Error in debug_gui: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
