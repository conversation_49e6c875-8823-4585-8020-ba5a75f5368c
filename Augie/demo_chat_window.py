#!/usr/bin/env python3
"""
Demo of The Printery AI Assistant chat window.
Shows the visual appearance and layout.
"""
import tkinter as tk
from tkinter import ttk, messagebox
import datetime
import uuid

class MockMessage:
    """Mock message for demo."""
    def __init__(self, role, content, timestamp=None):
        self.id = str(uuid.uuid4())
        self.role = role
        self.content = content
        self.timestamp = timestamp or datetime.datetime.now().isoformat()
        self.model = "gpt-3.5-turbo"

class ChatWindowDemo:
    """Demo of The Printery chat window."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("The Printery AI Assistant")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # The Printery theme colors
        self.colors = {
            'bg_primary': '#ffffff',        # White background
            'bg_secondary': '#fef7f7',      # Very light pink tint
            'bg_tertiary': '#f0f8ff',       # Very light blue tint
            'text_primary': '#000000',      # Black text (matching logo)
            'text_secondary': '#4a4a4a',    # Dark gray text
            'accent': '#20B2AA',            # Teal/turquoise (matching logo)
            'accent_hover': '#1a9999',      # Darker teal
            'success': '#28a745',           # Green
            'warning': '#ffc107',           # Yellow
            'danger': '#dc3545',            # Red
            'user_msg': '#FFB6C1',          # Light pink for user messages (matching logo pink)
            'assistant_msg': '#E0F8F8',     # Very light teal for assistant messages
            'pink_accent': '#FFB6C1',       # Light pink (matching logo)
            'teal_accent': '#20B2AA',       # Teal accent (matching logo)
            'border': '#e0e0e0',            # Light border
            'hover': '#f5f5f5',             # Hover state
            'printery_pink': '#FFB6C1',     # The Printery pink
            'printery_teal': '#20B2AA',     # The Printery teal
        }
        
        # Sample conversations and messages
        self.conversations = [
            {"title": "Welcome to The Printery", "active": True},
            {"title": "Project Planning Discussion", "active": False},
            {"title": "Creative Writing Session", "active": False},
            {"title": "Code Review and Debugging", "active": False},
        ]
        
        self.messages = [
            MockMessage("user", "Hello! I'd like to learn about The Printery's AI assistant capabilities."),
            MockMessage("assistant", "Welcome to The Printery AI Assistant! I'm here to help you with a wide range of tasks. I can assist with creative writing, business planning, technical questions, and much more. What would you like to explore today?"),
            MockMessage("user", "Can you help me brainstorm some marketing ideas for a print shop?"),
            MockMessage("assistant", "Absolutely! Here are some creative marketing ideas for The Printery:\n\n• Custom branded merchandise (business cards, letterheads, promotional materials)\n• Social media campaigns showcasing before/after print transformations\n• Partnership with local businesses for cross-promotion\n• Seasonal printing packages (holiday cards, wedding invitations)\n• Educational workshops on design and printing\n\nWould you like me to elaborate on any of these ideas?"),
        ]
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the chat window UI."""
        self.root.configure(bg=self.colors['bg_primary'])
        
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create three-panel layout
        self.setup_sidebar(main_frame)
        self.setup_chat_area(main_frame)
        self.setup_right_panel(main_frame)
    
    def setup_sidebar(self, parent):
        """Set up the left sidebar with conversations."""
        # Sidebar frame
        sidebar = tk.Frame(
            parent,
            bg=self.colors['bg_secondary'],
            width=250
        )
        sidebar.pack(side=tk.LEFT, fill=tk.Y)
        sidebar.pack_propagate(False)
        
        # Header
        header_frame = tk.Frame(sidebar, bg=self.colors['bg_secondary'])
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # The Printery logo area (placeholder)
        logo_frame = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        logo_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(
            logo_frame,
            text="🍒 The Printery",
            font=('Arial', 14, 'bold'),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        ).pack()
        
        # New chat button
        new_chat_btn = tk.Button(
            header_frame,
            text="+ New Chat",
            bg=self.colors['accent'],
            fg='white',
            border=0,
            font=('Arial', 10, 'bold'),
            cursor='hand2'
        )
        new_chat_btn.pack(fill=tk.X, pady=(0, 10))
        
        # Conversations list
        conv_frame = tk.Frame(sidebar, bg=self.colors['bg_secondary'])
        conv_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        for i, conv in enumerate(self.conversations):
            bg_color = self.colors['bg_tertiary'] if conv['active'] else self.colors['bg_secondary']
            
            item_frame = tk.Frame(conv_frame, bg=bg_color, cursor='hand2')
            item_frame.pack(fill=tk.X, pady=2)
            
            title_label = tk.Label(
                item_frame,
                text=conv['title'][:25] + ("..." if len(conv['title']) > 25 else ""),
                bg=bg_color,
                fg=self.colors['text_primary'],
                font=('Arial', 10, 'bold' if conv['active'] else 'normal'),
                anchor=tk.W
            )
            title_label.pack(fill=tk.X, padx=10, pady=(5, 0))
            
            preview_label = tk.Label(
                item_frame,
                text="Last message preview...",
                bg=bg_color,
                fg=self.colors['text_secondary'],
                font=('Arial', 9),
                anchor=tk.W
            )
            preview_label.pack(fill=tk.X, padx=10, pady=(0, 5))
    
    def setup_chat_area(self, parent):
        """Set up the main chat area."""
        # Chat area frame
        chat_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        chat_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Chat header
        header_frame = tk.Frame(
            chat_frame,
            bg=self.colors['bg_secondary'],
            height=60
        )
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # Title
        tk.Label(
            header_frame,
            text="Welcome to The Printery",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            font=('Arial', 14, 'bold')
        ).pack(side=tk.LEFT, padx=20, pady=15)
        
        # Model selector
        model_frame = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        model_frame.pack(side=tk.RIGHT, padx=20, pady=15)
        
        tk.Label(
            model_frame,
            text="Model:",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 10)
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        model_combo = ttk.Combobox(
            model_frame,
            values=["gpt-4o", "claude-3-5-sonnet", "gemini-1.5-pro"],
            state="readonly",
            width=20
        )
        model_combo.set("gpt-3.5-turbo")
        model_combo.pack(side=tk.LEFT)
        
        # Messages area
        messages_frame = tk.Frame(chat_frame, bg=self.colors['bg_primary'])
        messages_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Messages container with scrollbar
        messages_canvas = tk.Canvas(
            messages_frame,
            bg=self.colors['bg_primary'],
            highlightthickness=0
        )
        messages_scrollbar = ttk.Scrollbar(
            messages_frame,
            orient="vertical",
            command=messages_canvas.yview
        )
        messages_scrollable_frame = tk.Frame(
            messages_canvas,
            bg=self.colors['bg_primary']
        )
        
        messages_canvas.create_window((0, 0), window=messages_scrollable_frame, anchor="nw")
        messages_canvas.configure(yscrollcommand=messages_scrollbar.set)
        
        messages_canvas.pack(side="left", fill="both", expand=True)
        messages_scrollbar.pack(side="right", fill="y")
        
        # Add sample messages
        for message in self.messages:
            self.add_message_widget(messages_scrollable_frame, message)
        
        # Input area
        input_container = tk.Frame(
            chat_frame,
            bg=self.colors['bg_secondary'],
            height=100
        )
        input_container.pack(fill=tk.X, padx=20, pady=(0, 20))
        input_container.pack_propagate(False)
        
        input_frame = tk.Frame(input_container, bg=self.colors['bg_secondary'])
        input_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Text input
        input_text = tk.Text(
            input_frame,
            height=3,
            wrap=tk.WORD,
            font=('Arial', 11),
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            border=0,
            relief=tk.FLAT
        )
        input_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        input_text.insert('1.0', "Type your message here... (Ctrl+Enter to send)")
        
        # Buttons
        buttons_frame = tk.Frame(input_frame, bg=self.colors['bg_secondary'])
        buttons_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        send_btn = tk.Button(
            buttons_frame,
            text="Send",
            bg=self.colors['accent'],
            fg='white',
            border=0,
            font=('Arial', 10, 'bold'),
            cursor='hand2',
            width=8
        )
        send_btn.pack(pady=(0, 5))
        
        file_btn = tk.Button(
            buttons_frame,
            text="📎",
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            border=0,
            font=('Arial', 12),
            cursor='hand2',
            width=8
        )
        file_btn.pack()
    
    def setup_right_panel(self, parent):
        """Set up the right panel."""
        # Right panel frame
        right_panel = tk.Frame(
            parent,
            bg=self.colors['bg_secondary'],
            width=200
        )
        right_panel.pack(side=tk.RIGHT, fill=tk.Y)
        right_panel.pack_propagate(False)
        
        # Header
        tk.Label(
            right_panel,
            text="Settings",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            font=('Arial', 12, 'bold')
        ).pack(pady=20)
        
        # Assistant selector
        assistant_frame = tk.Frame(right_panel, bg=self.colors['bg_secondary'])
        assistant_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(
            assistant_frame,
            text="Assistant",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 10, 'bold')
        ).pack(anchor=tk.W)
        
        assistant_combo = ttk.Combobox(
            assistant_frame,
            values=["General Assistant", "Code Assistant", "Creative Writer"],
            state="readonly",
            width=18
        )
        assistant_combo.set("General Assistant")
        assistant_combo.pack(fill=tk.X, pady=(5, 0))
        
        # Theme selector
        theme_frame = tk.Frame(right_panel, bg=self.colors['bg_secondary'])
        theme_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(
            theme_frame,
            text="Theme",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 10, 'bold')
        ).pack(anchor=tk.W)
        
        theme_combo = ttk.Combobox(
            theme_frame,
            values=["The Printery Theme", "Dark Printery Theme", "Cherry Classic"],
            state="readonly",
            width=18
        )
        theme_combo.set("The Printery Theme")
        theme_combo.pack(fill=tk.X, pady=(5, 0))
        
        # Stats
        stats_frame = tk.Frame(right_panel, bg=self.colors['bg_secondary'])
        stats_frame.pack(fill=tk.X, padx=10, pady=20)
        
        tk.Label(
            stats_frame,
            text="Conversation Stats",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 10, 'bold')
        ).pack(anchor=tk.W)
        
        tk.Label(
            stats_frame,
            text="Messages: 4\nTokens: ~150",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            font=('Arial', 9),
            justify=tk.LEFT
        ).pack(anchor=tk.W, pady=(5, 0))
        
        # Settings button
        settings_frame = tk.Frame(right_panel, bg=self.colors['bg_secondary'])
        settings_frame.pack(fill=tk.X, padx=10, pady=20)
        
        settings_btn = tk.Button(
            settings_frame,
            text="⚙️ Advanced Settings",
            bg=self.colors['accent'],
            fg='white',
            border=0,
            font=('Arial', 10, 'bold'),
            command=self.show_settings_demo,
            cursor='hand2'
        )
        settings_btn.pack(fill=tk.X)
        
        info_label = tk.Label(
            settings_frame,
            text="Access all settings including\nthemes, assistants, MCP servers,\nmemory, and advanced options",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 8),
            justify=tk.CENTER
        )
        info_label.pack(pady=(5, 0))
    
    def add_message_widget(self, parent, message):
        """Add a message widget to the chat display."""
        # Message container
        msg_container = tk.Frame(parent, bg=self.colors['bg_primary'])
        msg_container.pack(fill=tk.X, pady=5)
        
        # Determine alignment and colors
        if message.role == 'user':
            anchor = tk.E
            bg_color = self.colors['user_msg']
            text_color = 'black'
            padx = (100, 0)
        else:
            anchor = tk.W
            bg_color = self.colors['assistant_msg']
            text_color = 'black'
            padx = (0, 100)
        
        # Message bubble
        bubble_frame = tk.Frame(msg_container, bg=self.colors['bg_primary'])
        bubble_frame.pack(anchor=anchor, padx=padx)
        
        # Message content
        msg_label = tk.Label(
            bubble_frame,
            text=message.content,
            bg=bg_color,
            fg=text_color,
            font=('Arial', 10),
            wraplength=400,
            justify=tk.LEFT,
            anchor=tk.W
        )
        msg_label.pack(padx=15, pady=10)
        
        # Timestamp
        timestamp = datetime.datetime.fromisoformat(message.timestamp).strftime("%H:%M")
        time_label = tk.Label(
            msg_container,
            text=timestamp,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 8)
        )
        time_label.pack(anchor=anchor, padx=padx)
    
    def show_settings_demo(self):
        """Show the tabbed settings demo."""
        messagebox.showinfo(
            "Tabbed Settings",
            "The tabbed settings window would open here with:\n\n" +
            "• General settings\n" +
            "• Themes (with The Printery colors)\n" +
            "• AI Assistants management\n" +
            "• MCP Servers configuration\n" +
            "• Memory & Context settings\n" +
            "• Advanced options\n\n" +
            "All organized in clean, professional tabs!"
        )
    
    def run(self):
        """Run the demo."""
        self.root.mainloop()

if __name__ == "__main__":
    print("🍒 The Printery AI Assistant - Chat Window Demo")
    print("=" * 50)
    print("Showing the visual appearance of:")
    print("✓ Three-panel layout (conversations, chat, settings)")
    print("✓ The Printery branding (pink and teal colors)")
    print("✓ Message bubbles with proper alignment")
    print("✓ Professional interface design")
    print("=" * 50)
    
    demo = ChatWindowDemo()
    demo.run()
