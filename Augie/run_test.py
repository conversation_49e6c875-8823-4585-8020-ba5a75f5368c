"""
Simple test script to verify core functionality of VoiceFlow AI.
"""
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set up basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all required modules can be imported."""
    logger.info("Testing module imports...")
    
    try:
        # Test core imports
        from src.llm.base import BaseLLM
        from src.voice.stt import SpeechToText
        from src.voice.tts import TextToSpeech
        from src.utils.document_processor import DocumentProcessor
        
        logger.info("✅ All core modules imported successfully!")
        return True
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False

def test_config():
    """Test that the configuration can be loaded."""
    logger.info("Testing configuration...")
    
    try:
        from src.utils.config import load_config
        import configparser
        
        # Try to load the test config
        config_path = Path(__file__).parent / 'tests' / 'test_config.ini'
        logger.info(f"Loading config from: {config_path}")
        
        # Check if file exists
        if not config_path.exists():
            logger.error(f"❌ Config file not found: {config_path}")
            return False
            
        # Read the config file directly to check its contents
        with open(config_path, 'r') as f:
            config_content = f.read()
            logger.debug(f"Config file content:\n{config_content}")
        
        # Load the config using our function
        config = load_config(config_path)
        
        # Verify it's a ConfigParser object
        if not isinstance(config, configparser.ConfigParser):
            logger.error(f"❌ Expected ConfigParser object, got {type(config)}")
            return False
            
        # Log the loaded config sections for debugging
        logger.debug(f"Config sections: {config.sections()}")
        
        # Check required sections
        required_sections = ['VOICE', 'OPENAI', 'ANTHROPIC', 'GEMINI', 'OLLAMA']
        missing_sections = [s for s in required_sections if not config.has_section(s)]
        
        if missing_sections:
            logger.error(f"❌ Missing configuration sections: {', '.join(missing_sections)}")
            return False
            
        # Check if we can access values
        try:
            # Check for required keys in DEFAULT section
            required_default_keys = ['app_name', 'version', 'log_level']
            missing_keys = [k for k in required_default_keys if not config.has_option('DEFAULT', k)]
            
            if missing_keys:
                logger.error(f"❌ Missing required keys in DEFAULT section: {', '.join(missing_keys)}")
                return False
                
            # Check that we can access values from sections
            test_values = [
                ('OPENAI', 'model'),
                ('VOICE', 'wake_word'),
                ('ANTHROPIC', 'model'),
                ('GEMINI', 'model'),
                ('OLLAMA', 'model')
            ]
            
            for section, option in test_values:
                if not config.has_option(section, option):
                    logger.error(f"❌ Missing required option '{option}' in section '{section}'")
                    return False
                value = config.get(section, option)
                logger.debug(f"Config value {section}.{option} = {value}")
            
            # Log some config values for debugging
            app_name = config.get('DEFAULT', 'app_name')
            log_level = config.get('DEFAULT', 'log_level')
            openai_model = config.get('OPENAI', 'model')
            
            logger.debug(f"App name: {app_name}")
            logger.debug(f"Log level: {log_level}")
            logger.debug(f"OpenAI model: {openai_model}")
            
            # Test getint and getfloat methods
            try:
                energy_threshold = config.getint('VOICE', 'energy_threshold')
                pause_threshold = config.getfloat('VOICE', 'pause_threshold')
                logger.debug(f"Voice energy threshold: {energy_threshold}, pause threshold: {pause_threshold}")
            except ValueError as e:
                logger.error(f"❌ Error parsing numeric config values: {e}")
                return False
            
            logger.info("✅ Configuration loaded and validated successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error accessing config values: {e}", exc_info=True)
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to load configuration: {e}", exc_info=True)
        return False

def main():
    """Run all tests and report results."""
    logger.info("🚀 Starting VoiceFlow AI test suite...")
    
    # Run tests
    tests = [
        ("Module Imports", test_imports),
        ("Configuration", test_config)
    ]
    
    results = []
    for name, test_func in tests:
        logger.info(f"\n🔍 Running test: {name}")
        result = test_func()
        results.append((name, result))
    
    # Print summary
    logger.info("\n📊 Test Results:")
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {name}")
    
    # Exit with appropriate code
    all_passed = all(result for _, result in results)
    if all_passed:
        logger.info("\n🎉 All tests passed successfully!")
    else:
        logger.error("\n❌ Some tests failed. Please check the logs above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
