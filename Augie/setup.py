""
Setup script for the VoiceFlow AI Assistant.

This script allows the package to be installed with pip.
"""
from setuptools import setup, find_packages

# Read the long description from README.md
with open('README.md', 'r', encoding='utf-8') as f:
    long_description = f.read()

# Read requirements from requirements.txt
with open('requirements.txt', 'r', encoding='utf-8') as f:
    requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="voiceflow-ai",
    version="0.1.0",
    description="A voice-enabled AI assistant with multi-LLM support",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Your Name",
    author_email="<EMAIL>",
    url="https://github.com/yourusername/voiceflow-ai",
    packages=find_packages(where='src'),
    package_dir={'': 'src'},
    include_package_data=True,
    install_requires=requirements,
    python_requires=">=3.8",
    entry_points={
        'console_scripts': [
            'voiceflow-ai=main:main',
            'voiceflow-ai-gui=gui:main',
        ],
    },
    classifiers=[
        'Development Status :: 3 - Alpha',
        'Intended Audience :: Developers',
        'Intended Audience :: End Users/Desktop',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Topic :: Multimedia :: Sound/Audio :: Speech',
        'Topic :: Scientific/Engineering :: Artificial Intelligence',
        'Operating System :: OS Independent',
    ],
    keywords='ai assistant voice llm chatgpt openai anthropic gemini ollama',
    project_urls={
        'Bug Reports': 'https://github.com/yourusername/voiceflow-ai/issues',
        'Source': 'https://github.com/yourusername/voiceflow-ai',
    },
)
