""
Graphical user interface entry point for the VoiceFlow AI Assistant.
"""
import asyncio
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from src.assistant import VoiceAssistant
from src.ui.main_window import MainWindow
from src.utils.config import load_config, find_config_file
from src.utils.logger import setup_logging

async def main():
    """Main entry point for the GUI application."""
    # Load configuration
    config_path = find_config_file()
    config = load_config(config_path)
    
    # Set up logging
    log_level = config.get('log_level', 'INFO')
    log_file = Path('logs') / 'voiceflow_ai_gui.log'
    setup_logging(log_level=log_level, log_file=str(log_file))
    
    # Initialize the assistant
    assistant = VoiceAssistant(config)
    await assistant.initialize()
    
    # Create and run the main window
    app = MainWindow(config, assistant)
    app.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nApplication terminated by user.")
    except Exception as e:
        print(f"Fatal error: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        sys.exit(1)
