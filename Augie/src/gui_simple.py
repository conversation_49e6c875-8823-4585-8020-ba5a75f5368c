"""
Simple GUI launcher that properly handles asyncio and Tkinter integration.
"""
import asyncio
import sys
import logging
import threading
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.assistant import VoiceAssistant
from src.ui.main_window import MainWindow
from src.utils.config import load_config
from src.utils.logger import setup_logging

logger = logging.getLogger(__name__)

def run_async_gui():
    """Run the GUI with proper asyncio integration."""
    
    # Load configuration
    config_path = Path(__file__).parent.parent / 'clean_config.ini'
    if not config_path.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    config = load_config(config_path)
    
    # Set up logging
    log_level = config.get('DEFAULT', 'log_level', fallback='INFO')
    log_file = Path('logs') / 'voiceflow_ai_gui.log'
    setup_logging(log_level=log_level, log_file=str(log_file))
    
    logger.info("Starting VoiceFlow AI Assistant GUI...")
    
    # Create event loop for asyncio operations
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    # Function to run the async initialization
    async def init_assistant():
        assistant = VoiceAssistant(config)
        await assistant.initialize()
        return assistant
    
    # Initialize the assistant
    logger.info("Initializing assistant...")
    assistant = loop.run_until_complete(init_assistant())
    logger.info("Assistant initialized successfully")
    
    # Create the main window
    logger.info("Creating main window...")
    app = MainWindow(config, assistant)
    
    # Start the asyncio event loop in a background thread
    def run_asyncio_loop():
        """Run the asyncio event loop in a background thread."""
        logger.debug("Starting asyncio event loop in background thread")
        try:
            loop.run_forever()
        except Exception as e:
            logger.error(f"Error in asyncio event loop: {e}", exc_info=True)
    
    # Start the background thread for asyncio
    asyncio_thread = threading.Thread(target=run_asyncio_loop, daemon=True)
    asyncio_thread.start()
    logger.debug("Asyncio event loop started in background thread")
    
    # Override the schedule_task method to use our loop
    original_schedule_task = app.schedule_task
    
    def schedule_task_with_loop(coro):
        """Schedule a task in our asyncio loop."""
        logger.debug(f"Scheduling task: {coro.__qualname__}")
        
        def run_task():
            task = asyncio.run_coroutine_threadsafe(coro, loop)
            app.tasks.append(task)
            
            def task_done(future):
                try:
                    future.result()
                    logger.debug("Task completed successfully")
                except Exception as e:
                    logger.error(f"Task failed: {e}", exc_info=True)
                finally:
                    if future in app.tasks:
                        app.tasks.remove(future)
            
            task.add_done_callback(task_done)
        
        # Schedule the task to run in the next Tkinter idle cycle
        app.after_idle(run_task)
    
    # Replace the schedule_task method
    app.schedule_task = schedule_task_with_loop
    
    # Set up proper cleanup
    original_on_close = app.on_close
    
    def on_close_with_cleanup():
        logger.info("Shutting down...")
        
        # Cancel all running tasks
        for task in app.tasks[:]:
            if not task.done():
                logger.debug(f"Cancelling task: {id(task)}")
                task.cancel()
        
        # Stop the asyncio loop
        loop.call_soon_threadsafe(loop.stop)
        
        # Call original cleanup
        original_on_close()
    
    app.on_close = on_close_with_cleanup
    
    try:
        # Run the Tkinter main loop
        logger.info("Starting Tkinter main loop...")
        app.mainloop()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
        
    finally:
        # Clean shutdown
        logger.info("Performing final cleanup...")
        
        # Stop the asyncio loop
        if loop.is_running():
            loop.call_soon_threadsafe(loop.stop)
        
        # Wait a bit for the loop to stop
        import time
        time.sleep(0.1)
        
        # Close the loop
        if not loop.is_closed():
            loop.close()
            
        logger.info("GUI shutdown complete")

if __name__ == "__main__":
    try:
        run_async_gui()
    except Exception as e:
        print(f"Error starting GUI: {e}", file=sys.stderr)
        logging.exception("Error starting GUI")
        sys.exit(1)