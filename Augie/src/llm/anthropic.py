"""
Anthropic API client implementation.
"""
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator, Union

import anthropic
from anthropic.types import Message, MessageParam

from .base import BaseLLM, LLMResponse

logger = logging.getLogger(__name__)

class AnthropicClient(BaseLLM):
    """Client for interacting with Anthropic's Claude models."""
    
    def _initialize_client(self) -> anthropic.AsyncAnthropic:
        """Initialize and return the Anthropic client."""
        api_key = self._get_config('api_key')
        if not api_key or api_key == 'your_anthropic_api_key':
            raise ValueError("Anthropic API key not found in configuration")
            
        return anthropic.AsyncAnthropic(
            api_key=api_key,
            timeout=self.timeout
        )
    
    def _format_messages(self, messages: List[Dict[str, str]]) -> List[MessageParam]:
        """Format messages for the Anthropic API."""
        formatted = []
        
        # Anthropic requires the first message to be from the user
        # and doesn't support system messages directly (must be in user message)
        system_prompt = ""
        
        # Extract system messages and user/assistant messages
        for msg in messages:
            role = msg['role']
            content = msg['content']
            
            if role == 'system':
                system_prompt += content + "\n"
            else:
                # Map to Anthropic's expected roles
                role = 'assistant' if role == 'assistant' else 'user'
                formatted.append({"role": role, "content": content})
        
        # If we have a system prompt, prepend it to the first user message
        if system_prompt and formatted and formatted[0]['role'] == 'user':
            formatted[0]['content'] = f"{system_prompt}\n\n{formatted[0]['content']}"
        
        return formatted
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Generate a response from Anthropic's Claude model."""
        # Get parameters with overrides from kwargs
        model = kwargs.get('model', self.model)
        temperature = kwargs.get('temperature', self.temperature)
        max_tokens = kwargs.get('max_tokens', self.max_tokens)
        stream = kwargs.get('stream', self.stream)
        
        # Format messages for the API
        formatted_messages = self._format_messages(messages)
        
        try:
            if stream:
                return self._stream_response(formatted_messages, model, temperature, max_tokens)
            
            # Non-streaming response
            response: Message = await self.client.messages.create(
                model=model,
                messages=formatted_messages,
                temperature=temperature,
                max_tokens=max_tokens,
            )
            
            # Extract the response content
            content = ""
            if response.content and hasattr(response.content[0], 'text'):
                content = response.content[0].text
            
            # Create a standardized response
            return self._create_response(
                content=content,
                model=response.model,
                prompt_tokens=response.usage.input_tokens,
                completion_tokens=response.usage.output_tokens,
                finish_reason=response.stop_reason,
                metadata={
                    "id": response.id,
                    "type": response.type,
                    "role": response.role,
                    "stop_reason": response.stop_reason,
                }
            )
            
        except Exception as e:
            logger.error(f"Error generating response from Anthropic: {e}")
            raise
    
    async def _stream_response(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float,
        max_tokens: int
    ) -> AsyncGenerator[str, None]:
        """Handle streaming responses from Anthropic."""
        try:
            async with self.client.messages.stream(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
            ) as stream:
                async for chunk in stream:
                    if chunk.type == 'content_block_delta' and chunk.delta.text:
                        yield chunk.delta.text
                    
        except Exception as e:
            logger.error(f"Error during streaming response: {e}")
            raise
    
    async def count_tokens(self, text: str, model: Optional[str] = None) -> int:
        """Count the number of tokens in the given text using Anthropic's tokenizer."""
        try:
            # Use the count_tokens method from the client
            return await self.client.count_tokens(text)
        except Exception as e:
            logger.error(f"Error counting tokens: {e}")
            # Fallback to a simple approximation if the API call fails
            return len(text) // 4  # Rough approximation
    
    async def get_embeddings(self, text: str, model: str = "claude-3-opus-20240229") -> List[float]:
        """
        Get embeddings using the Claude model.
        Note: As of my knowledge, Anthropic doesn't have a dedicated embeddings API,
        but we can use the model's internal representations as a workaround.
        """
        try:
            # This is a placeholder implementation
            # In a real scenario, you would use a dedicated embeddings model
            logger.warning("Anthropic doesn't have a dedicated embeddings API. Using a placeholder implementation.")
            
            # Return a dummy embedding
            return [0.0] * 1536  # Standard embedding size
            
        except Exception as e:
            logger.error(f"Error getting embeddings: {e}")
            raise
