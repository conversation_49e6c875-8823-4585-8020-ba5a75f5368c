"""
Cohere client for Cohere AI models.
"""
import asyncio
import aiohttp
import json
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator, Union

from .base import BaseLLM, LLMResponse

logger = logging.getLogger(__name__)

class CohereClient(BaseLLM):
    """Client for Cohere AI models."""
    
    def _initialize_client(self) -> aiohttp.ClientSession:
        """Initialize the Cohere client."""
        # Get Cohere-specific configuration
        self.api_key = self._get_config('api_key', '')
        self.base_url = self._get_config('base_url', 'https://api.cohere.ai/v1')
        
        if not self.api_key:
            raise ValueError("Cohere API key is required")
        
        # Create headers
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'Cherry-Studio-Clone/1.0'
        }
        
        # Create session with timeout
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        return aiohttp.ClientSession(
            headers=headers,
            timeout=timeout,
            connector=aiohttp.TCPConnector(limit=10)
        )
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Generate a response using Cohere."""
        try:
            # Convert messages to Cohere format
            formatted_messages = self._format_messages(messages)
            
            # Prepare the request payload
            payload = {
                'model': self.model or 'command-r-plus',
                'messages': formatted_messages,
                'max_tokens': kwargs.get('max_tokens', self.max_tokens),
                'temperature': kwargs.get('temperature', self.temperature),
                'p': kwargs.get('top_p', self.top_p),
                'stream': kwargs.get('stream', self.stream),
            }
            
            # Remove None values
            payload = {k: v for k, v in payload.items() if v is not None}
            
            logger.debug(f"Cohere request payload: {json.dumps(payload, indent=2)}")
            
            if payload.get('stream', False):
                return self._stream_response(payload)
            else:
                return await self._single_response(payload)
                
        except Exception as e:
            logger.error(f"Error in Cohere generate_response: {e}")
            raise
    
    async def _single_response(self, payload: Dict[str, Any]) -> str:
        """Handle non-streaming response."""
        url = f"{self.base_url}/chat"
        
        async with self.client.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                logger.error(f"Cohere API error {response.status}: {error_text}")
                raise Exception(f"Cohere API error {response.status}: {error_text}")
            
            data = await response.json()
            
            # Extract the response content
            if 'text' in data:
                content = data['text']
                logger.info(f"Cohere response received: {len(content)} characters")
                return content
            else:
                logger.error(f"Unexpected Cohere response format: {data}")
                raise Exception("Invalid response format from Cohere")
    
    async def _stream_response(self, payload: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """Handle streaming response."""
        url = f"{self.base_url}/chat"
        
        async with self.client.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                logger.error(f"Cohere API error {response.status}: {error_text}")
                raise Exception(f"Cohere API error {response.status}: {error_text}")
            
            async for line in response.content:
                line = line.decode('utf-8').strip()
                
                if line.startswith('data: '):
                    data_str = line[6:]  # Remove 'data: ' prefix
                    
                    if data_str == '[DONE]':
                        break
                    
                    try:
                        data = json.loads(data_str)
                        
                        if 'text' in data:
                            content = data['text']
                            if content:
                                yield content
                    
                    except json.JSONDecodeError:
                        continue  # Skip malformed JSON
    
    async def count_tokens(self, text: str) -> int:
        """Estimate token count for Cohere."""
        # Simple estimation: ~4 characters per token
        return len(text) // 4
    
    def _format_messages(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Format messages for Cohere."""
        formatted = []
        
        for message in messages:
            role = message.get('role', 'user')
            content = message.get('content', '')
            
            # Map roles to Cohere format
            if role == 'assistant':
                role = 'chatbot'
            elif role == 'system':
                role = 'system'
            else:
                role = 'user'
            
            formatted.append({
                'role': role,
                'message': content
            })
        
        return formatted
    
    async def get_available_models(self) -> List[str]:
        """Get list of available Cohere models."""
        # Cohere's main chat models
        return [
            'command-r-plus',
            'command-r',
            'command',
            'command-nightly',
            'command-light',
            'command-light-nightly'
        ]
    
    async def health_check(self) -> bool:
        """Check if Cohere API is accessible."""
        try:
            # Simple test request
            test_payload = {
                'model': self.model or 'command-r-plus',
                'messages': [{'role': 'user', 'message': 'test'}],
                'max_tokens': 1
            }
            
            url = f"{self.base_url}/chat"
            async with self.client.post(url, json=test_payload) as response:
                return response.status == 200
        except Exception as e:
            logger.error(f"Cohere health check failed: {e}")
            return False
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if hasattr(self, 'client') and self.client:
            await self.client.close()
