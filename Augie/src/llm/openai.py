"""
OpenAI API client implementation.
"""
import os
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator, Union
import tiktoken
from openai import OpenAI, AsyncOpenAI
from openai.types.chat import ChatCompletionMessageParam

from .base import BaseLLM, LLMResponse

logger = logging.getLogger(__name__)

class OpenAIClient(BaseLLM):
    """Client for interacting with OpenAI's chat models."""
    
    def _initialize_client(self) -> AsyncOpenAI:
        """Initialize and return the OpenAI client."""
        api_key = self._get_config('api_key')
        
        # Allow test keys (starting with 'test_' or 'sk-test-') in test environment
        is_test_key = api_key and (api_key.startswith('test_') or api_key.startswith('sk-test-'))
        
        if not api_key or (not is_test_key and api_key == 'your_openai_api_key'):
            raise ValueError("OpenAI API key not found in configuration")
            
        base_url = self._get_config('base_url', 'https://api.openai.com/v1')
        
        return AsyncOpenAI(
            api_key=api_key,
            base_url=base_url,
            timeout=self.timeout
        )
    
    def _format_messages(self, messages: List[Dict[str, str]]) -> List[ChatCompletionMessageParam]:
        """Format messages for the OpenAI API."""
        formatted = []
        for msg in messages:
            role = msg['role']
            content = msg['content']
            
            # Map common role names to OpenAI's expected roles
            if role == 'assistant':
                role = 'assistant'
            elif role == 'system':
                role = 'system'
            else:  # user, human, etc.
                role = 'user'
                
            formatted.append({"role": role, "content": content})
            
        return formatted
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Generate a response from OpenAI's chat model."""
        # Get parameters with overrides from kwargs
        model = kwargs.get('model', self.model)
        temperature = kwargs.get('temperature', self.temperature)
        max_tokens = kwargs.get('max_tokens', self.max_tokens)
        stream = kwargs.get('stream', self.stream)
        
        # Format messages for the API
        formatted_messages = self._format_messages(messages)
        
        try:
            if stream:
                return self._stream_response(formatted_messages, model, temperature, max_tokens)
            
            # Non-streaming response
            response = await self.client.chat.completions.create(
                model=model,
                messages=formatted_messages,
                temperature=temperature,
                max_tokens=max_tokens,
            )
            
            # Extract the response content
            content = response.choices[0].message.content or ""
            
            # Create a standardized response
            return self._create_response(
                content=content,
                model=response.model,
                prompt_tokens=response.usage.prompt_tokens,
                completion_tokens=response.usage.completion_tokens,
                finish_reason=response.choices[0].finish_reason,
                metadata={
                    "id": response.id,
                    "created": response.created,
                    "system_fingerprint": response.system_fingerprint
                }
            )
            
        except Exception as e:
            logger.error(f"Error generating response from OpenAI: {e}")
            raise
    
    async def _stream_response(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float,
        max_tokens: int
    ) -> AsyncGenerator[str, None]:
        """Handle streaming responses from OpenAI."""
        try:
            response = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
            
            collected_chunks = []
            collected_content = ""
            
            async for chunk in response:
                if not chunk.choices:
                    continue
                    
                delta = chunk.choices[0].delta
                if delta and delta.content:
                    content = delta.content
                    collected_chunks.append(chunk)
                    collected_content += content
                    yield content
            
            # After streaming is complete, you could log or process the full response
            logger.debug(f"Completed streaming response with {len(collected_chunks)} chunks")
            
        except Exception as e:
            logger.error(f"Error during streaming response: {e}")
            raise
    
    async def count_tokens(self, text: str, model: Optional[str] = None) -> int:
        """Count the number of tokens in the given text."""
        model_name = model or self.model
        
        # Special handling for different model families
        if 'gpt-4' in model_name or 'gpt-3.5' in model_name:
            encoding = tiktoken.encoding_for_model(model_name)
        else:
            # Default to cl100k_base which works for most models
            encoding = tiktoken.get_encoding("cl100k_base")
            
        return len(encoding.encode(text))
    
    async def get_embeddings(self, text: str, model: str = "text-embedding-3-small") -> List[float]:
        """Get embeddings for the given text."""
        try:
            response = await self.client.embeddings.create(
                input=text,
                model=model
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error getting embeddings: {e}")
            raise
    
    async def generate_image(
        self,
        prompt: str,
        model: str = "dall-e-3",
        size: str = "1024x1024",
        quality: str = "standard",
        n: int = 1
    ) -> List[str]:
        """Generate images using DALL-E."""
        try:
            response = await self.client.images.generate(
                model=model,
                prompt=prompt,
                size=size,
                quality=quality,
                n=n,
                response_format="url"
            )
            
            return [img.url for img in response.data if hasattr(img, 'url') and img.url]
            
        except Exception as e:
            logger.error(f"Error generating image: {e}")
            raise
