"""
Perplexity AI client for Perplexity models.
"""
import asyncio
import aiohttp
import json
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator, Union

from .base import BaseLLM, LLMResponse

logger = logging.getLogger(__name__)

class PerplexityClient(BaseLLM):
    """Client for Perplexity AI models."""
    
    def _initialize_client(self) -> aiohttp.ClientSession:
        """Initialize the Perplexity client."""
        # Get Perplexity-specific configuration
        self.api_key = self._get_config('api_key', '')
        self.base_url = self._get_config('base_url', 'https://api.perplexity.ai')
        
        if not self.api_key:
            raise ValueError("Perplexity API key is required")
        
        # Create headers
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'Cherry-Studio-Clone/1.0'
        }
        
        # Create session with timeout
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        return aiohttp.ClientSession(
            headers=headers,
            timeout=timeout,
            connector=aiohttp.TCPConnector(limit=10)
        )
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Generate a response using Perplexity."""
        try:
            # Prepare the request payload
            payload = {
                'model': self.model or 'llama-3.1-sonar-large-128k-online',
                'messages': self._format_messages(messages),
                'max_tokens': kwargs.get('max_tokens', self.max_tokens),
                'temperature': kwargs.get('temperature', self.temperature),
                'top_p': kwargs.get('top_p', self.top_p),
                'stream': kwargs.get('stream', self.stream),
                'return_citations': kwargs.get('return_citations', True),
                'return_images': kwargs.get('return_images', False),
            }
            
            # Remove None values
            payload = {k: v for k, v in payload.items() if v is not None}
            
            logger.debug(f"Perplexity request payload: {json.dumps(payload, indent=2)}")
            
            if payload.get('stream', False):
                return self._stream_response(payload)
            else:
                return await self._single_response(payload)
                
        except Exception as e:
            logger.error(f"Error in Perplexity generate_response: {e}")
            raise
    
    async def _single_response(self, payload: Dict[str, Any]) -> str:
        """Handle non-streaming response."""
        url = f"{self.base_url}/chat/completions"
        
        async with self.client.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                logger.error(f"Perplexity API error {response.status}: {error_text}")
                raise Exception(f"Perplexity API error {response.status}: {error_text}")
            
            data = await response.json()
            
            # Extract the response content
            if 'choices' in data and len(data['choices']) > 0:
                choice = data['choices'][0]
                content = choice['message']['content']
                
                # Add citations if available
                if 'citations' in data and data['citations']:
                    content += "\n\n**Sources:**\n"
                    for i, citation in enumerate(data['citations'], 1):
                        content += f"{i}. {citation}\n"
                
                logger.info(f"Perplexity response received: {len(content)} characters")
                return content
            else:
                logger.error(f"Unexpected Perplexity response format: {data}")
                raise Exception("Invalid response format from Perplexity")
    
    async def _stream_response(self, payload: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """Handle streaming response."""
        url = f"{self.base_url}/chat/completions"
        
        async with self.client.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                logger.error(f"Perplexity API error {response.status}: {error_text}")
                raise Exception(f"Perplexity API error {response.status}: {error_text}")
            
            async for line in response.content:
                line = line.decode('utf-8').strip()
                
                if line.startswith('data: '):
                    data_str = line[6:]  # Remove 'data: ' prefix
                    
                    if data_str == '[DONE]':
                        break
                    
                    try:
                        data = json.loads(data_str)
                        
                        if 'choices' in data and len(data['choices']) > 0:
                            choice = data['choices'][0]
                            
                            if 'delta' in choice and 'content' in choice['delta']:
                                content = choice['delta']['content']
                                if content:
                                    yield content
                    
                    except json.JSONDecodeError:
                        continue  # Skip malformed JSON
    
    async def count_tokens(self, text: str) -> int:
        """Estimate token count for Perplexity."""
        # Simple estimation: ~4 characters per token
        return len(text) // 4
    
    def _format_messages(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Format messages for Perplexity (OpenAI-compatible format)."""
        formatted = []
        
        for message in messages:
            role = message.get('role', 'user')
            content = message.get('content', '')
            
            # Perplexity supports OpenAI-compatible format
            formatted.append({
                'role': role,
                'content': content
            })
        
        return formatted
    
    async def get_available_models(self) -> List[str]:
        """Get list of available Perplexity models."""
        # Perplexity's available models
        return [
            'llama-3.1-sonar-large-128k-online',
            'llama-3.1-sonar-small-128k-online',
            'llama-3.1-sonar-large-128k-chat',
            'llama-3.1-sonar-small-128k-chat',
            'llama-3.1-8b-instruct',
            'llama-3.1-70b-instruct',
            'mixtral-8x7b-instruct',
            'codellama-70b-instruct'
        ]
    
    async def health_check(self) -> bool:
        """Check if Perplexity API is accessible."""
        try:
            # Simple test request
            test_payload = {
                'model': self.model or 'llama-3.1-sonar-large-128k-online',
                'messages': [{'role': 'user', 'content': 'test'}],
                'max_tokens': 1
            }
            
            url = f"{self.base_url}/chat/completions"
            async with self.client.post(url, json=test_payload) as response:
                return response.status == 200
        except Exception as e:
            logger.error(f"Perplexity health check failed: {e}")
            return False
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if hasattr(self, 'client') and self.client:
            await self.client.close()
