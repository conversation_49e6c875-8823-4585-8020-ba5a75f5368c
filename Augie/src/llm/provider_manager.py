"""
LLM Provider Manager for dynamic provider switching and model discovery.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Type
from dataclasses import dataclass

from .base import BaseLL<PERSON>
from .openai import OpenAIClient
from .anthropic import AnthropicClient
from .gemini import Gemini<PERSON>lient
from .ollama import OllamaClient
from .lmstudio import LMStudioClient
from .vllm import VLL<PERSON>lient
from .cohere import CohereClient
from .mistral import MistralClient
from .perplexity import PerplexityClient
from .groq import GroqClient

logger = logging.getLogger(__name__)

@dataclass
class ProviderInfo:
    """Information about an LLM provider."""
    name: str
    display_name: str
    description: str
    client_class: Type[BaseLLM]
    requires_api_key: bool = True
    is_local: bool = False
    supports_streaming: bool = True
    supports_vision: bool = False
    supports_function_calling: bool = False
    default_models: List[str] = None
    
    def __post_init__(self):
        if self.default_models is None:
            self.default_models = []

class ProviderManager:
    """Manages LLM providers and their capabilities."""
    
    def __init__(self, config):
        self.config = config
        self.providers = self._initialize_providers()
        self.active_clients: Dict[str, BaseLLM] = {}
    
    def _initialize_providers(self) -> Dict[str, ProviderInfo]:
        """Initialize provider information."""
        providers = {
            'openai': ProviderInfo(
                name='openai',
                display_name='OpenAI',
                description='GPT models from OpenAI',
                client_class=OpenAIClient,
                requires_api_key=True,
                is_local=False,
                supports_streaming=True,
                supports_vision=True,
                supports_function_calling=True,
                default_models=['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo']
            ),
            'anthropic': ProviderInfo(
                name='anthropic',
                display_name='Anthropic',
                description='Claude models from Anthropic',
                client_class=AnthropicClient,
                requires_api_key=True,
                is_local=False,
                supports_streaming=True,
                supports_vision=True,
                supports_function_calling=True,
                default_models=['claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022', 'claude-3-opus-20240229']
            ),
            'gemini': ProviderInfo(
                name='gemini',
                display_name='Google Gemini',
                description='Gemini models from Google',
                client_class=GeminiClient,
                requires_api_key=True,
                is_local=False,
                supports_streaming=True,
                supports_vision=True,
                supports_function_calling=True,
                default_models=['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-pro']
            ),
            'ollama': ProviderInfo(
                name='ollama',
                display_name='Ollama',
                description='Local models via Ollama',
                client_class=OllamaClient,
                requires_api_key=False,
                is_local=True,
                supports_streaming=True,
                supports_vision=False,
                supports_function_calling=True,
                default_models=['llama3.1:8b', 'llama3.1:70b', 'mistral:7b', 'codellama:7b']
            ),
            'lmstudio': ProviderInfo(
                name='lmstudio',
                display_name='LM Studio',
                description='Local models via LM Studio',
                client_class=LMStudioClient,
                requires_api_key=False,
                is_local=True,
                supports_streaming=True,
                supports_vision=False,
                supports_function_calling=True,
                default_models=['local-model']
            ),
            'vllm': ProviderInfo(
                name='vllm',
                display_name='vLLM',
                description='High-performance local inference',
                client_class=VLLMClient,
                requires_api_key=False,
                is_local=True,
                supports_streaming=True,
                supports_vision=False,
                supports_function_calling=True,
                default_models=['meta-llama/Llama-3.1-8B-Instruct', 'meta-llama/Llama-3.1-70B-Instruct']
            ),
            'cohere': ProviderInfo(
                name='cohere',
                display_name='Cohere',
                description='Command models from Cohere',
                client_class=CohereClient,
                requires_api_key=True,
                is_local=False,
                supports_streaming=True,
                supports_vision=False,
                supports_function_calling=True,
                default_models=['command-r-plus', 'command-r', 'command']
            ),
            'mistral': ProviderInfo(
                name='mistral',
                display_name='Mistral AI',
                description='Mistral and Mixtral models',
                client_class=MistralClient,
                requires_api_key=True,
                is_local=False,
                supports_streaming=True,
                supports_vision=False,
                supports_function_calling=True,
                default_models=['mistral-large-latest', 'mistral-medium-latest', 'mistral-small-latest']
            ),
            'perplexity': ProviderInfo(
                name='perplexity',
                display_name='Perplexity',
                description='Models with web search capabilities',
                client_class=PerplexityClient,
                requires_api_key=True,
                is_local=False,
                supports_streaming=True,
                supports_vision=False,
                supports_function_calling=False,
                default_models=['llama-3.1-sonar-large-128k-online', 'llama-3.1-sonar-small-128k-online']
            ),
            'groq': ProviderInfo(
                name='groq',
                display_name='Groq',
                description='Ultra-fast inference with Groq chips',
                client_class=GroqClient,
                requires_api_key=True,
                is_local=False,
                supports_streaming=True,
                supports_vision=False,
                supports_function_calling=True,
                default_models=['llama-3.1-70b-versatile', 'llama-3.1-8b-instant', 'mixtral-8x7b-32768']
            )
        }
        
        return providers
    
    def get_provider_info(self, provider_name: str) -> Optional[ProviderInfo]:
        """Get information about a provider."""
        return self.providers.get(provider_name.lower())
    
    def get_all_providers(self) -> List[ProviderInfo]:
        """Get all available providers."""
        return list(self.providers.values())
    
    def get_local_providers(self) -> List[ProviderInfo]:
        """Get providers that run locally."""
        return [provider for provider in self.providers.values() if provider.is_local]
    
    def get_cloud_providers(self) -> List[ProviderInfo]:
        """Get cloud-based providers."""
        return [provider for provider in self.providers.values() if not provider.is_local]
    
    def get_providers_with_vision(self) -> List[ProviderInfo]:
        """Get providers that support vision/image processing."""
        return [provider for provider in self.providers.values() if provider.supports_vision]
    
    def get_providers_with_function_calling(self) -> List[ProviderInfo]:
        """Get providers that support function calling."""
        return [provider for provider in self.providers.values() if provider.supports_function_calling]
    
    async def create_client(self, provider_name: str) -> Optional[BaseLLM]:
        """Create a client for the specified provider."""
        provider_info = self.get_provider_info(provider_name)
        if not provider_info:
            logger.error(f"Unknown provider: {provider_name}")
            return None
        
        try:
            # Check if we already have an active client
            if provider_name in self.active_clients:
                return self.active_clients[provider_name]
            
            # Create new client
            client = provider_info.client_class(self.config)
            self.active_clients[provider_name] = client
            
            logger.info(f"Created client for provider: {provider_name}")
            return client
            
        except Exception as e:
            logger.error(f"Failed to create client for {provider_name}: {e}")
            return None
    
    async def health_check_provider(self, provider_name: str) -> bool:
        """Check if a provider is healthy and accessible."""
        try:
            client = await self.create_client(provider_name)
            if not client:
                return False
            
            # Check if the client has a health_check method
            if hasattr(client, 'health_check'):
                return await client.health_check()
            else:
                # Fallback: try to create a simple request
                try:
                    test_messages = [{"role": "user", "content": "test"}]
                    response = await client.generate_response(test_messages)
                    return bool(response)
                except:
                    return False
                    
        except Exception as e:
            logger.error(f"Health check failed for {provider_name}: {e}")
            return False
    
    async def get_available_models(self, provider_name: str) -> List[str]:
        """Get available models for a provider."""
        try:
            client = await self.create_client(provider_name)
            if not client:
                provider_info = self.get_provider_info(provider_name)
                return provider_info.default_models if provider_info else []
            
            # Check if the client has a get_available_models method
            if hasattr(client, 'get_available_models'):
                return await client.get_available_models()
            else:
                # Fallback to default models
                provider_info = self.get_provider_info(provider_name)
                return provider_info.default_models if provider_info else []
                
        except Exception as e:
            logger.error(f"Error getting models for {provider_name}: {e}")
            provider_info = self.get_provider_info(provider_name)
            return provider_info.default_models if provider_info else []
    
    async def get_all_available_models(self) -> Dict[str, List[str]]:
        """Get all available models from all providers."""
        all_models = {}
        
        for provider_name in self.providers.keys():
            try:
                models = await self.get_available_models(provider_name)
                if models:
                    all_models[provider_name] = models
            except Exception as e:
                logger.error(f"Error getting models for {provider_name}: {e}")
                continue
        
        return all_models
    
    def get_provider_for_model(self, model_name: str) -> Optional[str]:
        """Determine which provider a model belongs to based on naming patterns."""
        model_lower = model_name.lower()
        
        # Check for explicit provider prefixes
        if model_name.startswith('ollama/'):
            return 'ollama'
        elif model_name.startswith('lmstudio/'):
            return 'lmstudio'
        elif model_name.startswith('vllm/'):
            return 'vllm'
        
        # Check model name patterns
        if any(pattern in model_lower for pattern in ['gpt-', 'chatgpt', 'davinci']):
            return 'openai'
        elif any(pattern in model_lower for pattern in ['claude-', 'claude']):
            return 'anthropic'
        elif any(pattern in model_lower for pattern in ['gemini', 'bard']):
            return 'gemini'
        elif any(pattern in model_lower for pattern in ['mistral', 'mixtral', 'codestral']):
            return 'mistral'
        elif any(pattern in model_lower for pattern in ['command-', 'command']):
            return 'cohere'
        elif any(pattern in model_lower for pattern in ['sonar-', 'pplx-']):
            return 'perplexity'
        elif any(pattern in model_lower for pattern in ['llama', 'mixtral']) and 'groq' not in model_lower:
            # Could be Groq, Ollama, or others - check context
            if any(pattern in model_lower for pattern in ['versatile', 'instant']):
                return 'groq'
            else:
                return 'ollama'  # Default for llama models
        
        # Default fallback
        return None
    
    def get_model_display_name(self, model_name: str, provider_name: str = None) -> str:
        """Get a user-friendly display name for a model."""
        if not provider_name:
            provider_name = self.get_provider_for_model(model_name)
        
        provider_info = self.get_provider_info(provider_name) if provider_name else None
        provider_display = provider_info.display_name if provider_info else "Unknown"
        
        # Clean up model name for display
        display_name = model_name
        
        # Remove provider prefixes
        if '/' in display_name:
            display_name = display_name.split('/', 1)[1]
        
        # Add provider context
        return f"{display_name} ({provider_display})"
    
    async def cleanup(self):
        """Clean up all active clients."""
        for client in self.active_clients.values():
            try:
                if hasattr(client, '__aexit__'):
                    await client.__aexit__(None, None, None)
                elif hasattr(client, 'close'):
                    await client.close()
            except Exception as e:
                logger.error(f"Error cleaning up client: {e}")
        
        self.active_clients.clear()
