"""
Main entry point for the VoiceFlow AI Assistant.
"""
import asyncio
import signal
import sys
import os
from pathlib import Path
from typing import Optional, Dict, Any

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.assistant import VoiceAssistant
from src.utils.config import load_config, find_config_file, save_config
from src.utils.logger import setup_logging

# Global variable to store the assistant instance
assistant: Optional[VoiceAssistant] = None

async def shutdown(signal: Optional[signal.Signals] = None) -> None:
    """Cleanup tasks tied to the service's shutdown."""
    if signal:
        print(f"\nReceived exit signal {signal.name}...")
    
    print("Shutting down gracefully...")
    
    global assistant
    if assistant:
        # Perform any cleanup needed for the assistant
        pass
    
    # Cancel all running tasks
    tasks = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
    for task in tasks:
        task.cancel()
    
    if tasks:
        print(f"Cancelling {len(tasks)} pending tasks...")
        await asyncio.gather(*tasks, return_exceptions=True)
    
    print("Shutdown complete.")
    asyncio.get_event_loop().stop()

def handle_exception(loop: asyncio.AbstractEventLoop, context: Dict[str, Any]) -> None:
    """Handle uncaught exceptions in the event loop."""
    # Log the error
    error_msg = context.get('exception', context['message'])
    print(f"Unhandled exception: {error_msg}")
    
    # Log the exception
    if 'exception' in context:
        import traceback
        print(''.join(traceback.format_exception(
            type(context['exception']),
            context['exception'],
            context['exception'].__traceback__
        )))
    
    # Schedule the shutdown
    asyncio.create_task(shutdown())

async def run_console_ui(assistant: VoiceAssistant) -> None:
    """Run the assistant in console mode."""
    print("\n" + "=" * 50)
    print("VoiceFlow AI Assistant - Console Mode")
    print("Type 'exit' or press Ctrl+C to quit")
    print("Type 'voice' to switch to voice mode")
    print("=" * 50 + "\n")
    
    try:
        while True:
            try:
                # Get user input
                try:
                    user_input = input("You: ")
                except (EOFError, KeyboardInterrupt):
                    print("\nExiting...")
                    break
                
                # Check for exit command
                if user_input.lower() in ('exit', 'quit', 'q'):
                    print("Goodbye!")
                    break
                
                # Check for voice mode
                if user_input.lower() == 'voice':
                    print("Switching to voice mode...")
                    await run_voice_mode(assistant)
                    continue
                
                # Process the input
                if user_input.strip():
                    response = await assistant.process_text_input(user_input)
                    print(f"\nAssistant: {response}\n")
                
            except Exception as e:
                print(f"Error: {str(e)}")
                continue
                
    except asyncio.CancelledError:
        pass

async def run_voice_mode(assistant: VoiceAssistant) -> None:
    """Run the assistant in voice mode."""
    print("\nVoice mode activated. Say 'hey assistant' to start speaking.")
    print("Say 'exit' or press Ctrl+C to return to console mode.")
    
    try:
        while True:
            try:
                await assistant.process_voice_input()
                
            except KeyboardInterrupt:
                print("\nReturning to console mode...")
                break
                
            except Exception as e:
                print(f"Error in voice mode: {str(e)}")
                continue
                
    except asyncio.CancelledError:
        pass

async def initialize_assistant(config_path: Optional[str] = None) -> VoiceAssistant:
    """Initialize the VoiceAssistant with configuration."""
    # Load configuration
    config = load_config(config_path)
    
    # Set up logging
    log_level = config.get('DEFAULT', 'log_level', fallback='INFO')
    log_file = os.path.join('logs', 'voiceflow_ai.log')
    setup_logging(log_level=log_level, log_file=log_file)
    
    # Create and initialize the assistant
    assistant = VoiceAssistant(config)
    await assistant.initialize()
    
    return assistant

def main() -> int:
    """Main entry point for the application."""
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='VoiceFlow AI Assistant')
    parser.add_argument('--config', type=str, default='config_new.ini', 
                      help='Path to configuration file (default: config_new.ini)')
    parser.add_argument('--voice', action='store_true', help='Run in voice mode')
    parser.add_argument('--console', action='store_true', help='Run in console mode')
    args = parser.parse_args()
    
    # Set up event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    # Handle keyboard interrupt and termination signals
    for sig in (signal.SIGINT, signal.SIGTERM):
        loop.add_signal_handler(
            sig,
            lambda s=sig: asyncio.create_task(
                shutdown(s, loop)
            )
        )
    
    # Set exception handler
    loop.set_exception_handler(handle_exception)
    
    try:
        # Initialize the assistant
        assistant_future = asyncio.ensure_future(initialize_assistant(args.config))
        global assistant
        assistant = loop.run_until_complete(assistant_future)
        
        # Run the appropriate mode
        if args.voice:
            loop.run_until_complete(run_voice_mode(assistant))
        else:
            loop.run_until_complete(run_console_ui(assistant))
        
        return 0
        
    except Exception as e:
        print(f"Fatal error: {str(e)}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        return 1
        
    finally:
        # Clean up
        loop.run_until_complete(shutdown())
        loop.close()

if __name__ == "__main__":
    sys.exit(main())
