"""
Basic MCP server configurations and setup.
"""
import logging
import json
from pathlib import Path
from typing import Dict, List, Any
from .types import MCPServer, MCPTransport

logger = logging.getLogger(__name__)

def create_basic_mcp_servers() -> Dict[str, MCPServer]:
    """Create basic MCP server configurations."""
    
    servers = {}
    
    # Filesystem server
    filesystem_server = MCPServer(
        name="filesystem",
        display_name="File System Tools",
        description="Tools for file and directory operations",
        transport=MCPTransport.STDIO,
        command=["npx", "-y", "@modelcontextprotocol/server-filesystem"],
        args=[str(Path.cwd())],  # Use current working directory
        enabled=True,
        auto_start=True,
        env={}
    )
    servers["filesystem"] = filesystem_server
    
    # Git server
    git_server = MCPServer(
        name="git",
        display_name="Git Tools",
        description="Git repository management tools",
        transport=MCPTransport.STDIO,
        command=["npx", "-y", "@modelcontextprotocol/server-git"],
        args=["--repository", str(Path.cwd())],
        enabled=True,
        auto_start=True,
        env={}
    )
    servers["git"] = git_server
    
    # Web search server (requires API key)
    search_server = MCPServer(
        name="brave_search",
        display_name="Web Search",
        description="Web search using Brave Search API",
        transport=MCPTransport.STDIO,
        command=["npx", "-y", "@modelcontextprotocol/server-brave-search"],
        args=[],
        enabled=False,  # Disabled by default - requires API key
        auto_start=False,
        env={"BRAVE_API_KEY": "your_brave_api_key_here"}
    )
    servers["brave_search"] = search_server
    
    return servers

def setup_mcp_servers_config(data_dir: str = "data") -> bool:
    """Set up MCP servers configuration file if it doesn't exist."""
    try:
        data_path = Path(data_dir)
        data_path.mkdir(exist_ok=True)
        
        servers_file = data_path / "mcp_servers.json"
        
        if not servers_file.exists():
            # Create basic server configurations
            servers = create_basic_mcp_servers()
            
            # Convert to JSON format
            servers_data = {
                name: server.to_dict()
                for name, server in servers.items()
            }
            
            # Save to file
            with open(servers_file, 'w', encoding='utf-8') as f:
                json.dump(servers_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Created MCP servers configuration with {len(servers)} servers")
            return True
        else:
            logger.info("MCP servers configuration already exists")
            return True
            
    except Exception as e:
        logger.error(f"Error setting up MCP servers config: {e}")
        return False

def validate_mcp_server_requirements() -> Dict[str, bool]:
    """Validate that MCP server requirements are met."""
    requirements = {}
    
    try:
        import subprocess
        
        # Check if Node.js is available
        try:
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            requirements['nodejs'] = result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            requirements['nodejs'] = False
        
        # Check if npm/npx is available
        try:
            result = subprocess.run(['npx', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            requirements['npx'] = result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            requirements['npx'] = False
        
        # Check if git is available (for git server)
        try:
            result = subprocess.run(['git', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            requirements['git'] = result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            requirements['git'] = False
            
    except Exception as e:
        logger.error(f"Error validating MCP requirements: {e}")
        requirements = {'nodejs': False, 'npx': False, 'git': False}
    
    return requirements

def get_mcp_setup_instructions() -> str:
    """Get setup instructions for MCP servers."""
    
    requirements = validate_mcp_server_requirements()
    
    instructions = """MCP Server Setup Instructions:

The Printery AI Assistant uses MCP (Model Context Protocol) servers to provide additional tools and capabilities.

Current Requirements Status:"""
    
    for req, status in requirements.items():
        status_text = "✅ Available" if status else "❌ Missing"
        instructions += f"\n- {req}: {status_text}"
    
    if not all(requirements.values()):
        instructions += """

To install missing requirements:

1. Install Node.js:
   - Visit https://nodejs.org/
   - Download and install the latest LTS version
   - This will also install npm and npx

2. Install Git (if not already installed):
   - Visit https://git-scm.com/
   - Download and install for your operating system

3. Restart the application after installing requirements

Available MCP Servers:
- File System Tools: File and directory operations
- Git Tools: Git repository management
- Web Search: Web search capabilities (requires API key)
"""
    else:
        instructions += """

✅ All requirements are met!

Available MCP Servers:
- File System Tools: File and directory operations
- Git Tools: Git repository management  
- Web Search: Web search capabilities (requires Brave API key)

MCP servers will be automatically configured and started when needed.
"""
    
    return instructions
