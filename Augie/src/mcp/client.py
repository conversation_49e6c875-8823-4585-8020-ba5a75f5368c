"""
MCP (Model Context Protocol) client implementation.
"""
import asyncio
import json
import logging
import os
import subprocess
import uuid
from typing import Dict, List, Any, Optional, AsyncGenerator
from pathlib import Path

from .types import (
    MCPServer, MCPTool, MCPResource, MCPToolCall, MCPToolResult, 
    MCPResourceContent, MCPTransport
)

logger = logging.getLogger(__name__)

class MCPClient:
    """Client for communicating with MCP servers."""
    
    def __init__(self, server: MCPServer):
        self.server = server
        self.process: Optional[subprocess.Popen] = None
        self.connected = False
        self.tools: List[MCPTool] = []
        self.resources: List[MCPResource] = []
        self.request_id = 0
    
    async def connect(self) -> bool:
        """Connect to the MCP server."""
        try:
            if self.server.transport == MCPTransport.STDIO:
                return await self._connect_stdio()
            elif self.server.transport == MCPTransport.HTTP:
                return await self._connect_http()
            elif self.server.transport == MCPTransport.WEBSOCKET:
                return await self._connect_websocket()
            else:
                logger.error(f"Unsupported transport: {self.server.transport}")
                return False
        except Exception as e:
            logger.error(f"Error connecting to MCP server {self.server.name}: {e}")
            return False
    
    async def _connect_stdio(self) -> bool:
        """Connect to MCP server via STDIO."""
        if not self.server.command:
            logger.error(f"No command specified for STDIO server {self.server.name}")
            return False
        
        try:
            # Start the server process
            cmd = self.server.command + (self.server.args or [])
            env = os.environ.copy()
            if self.server.env:
                env.update(self.server.env)
            
            self.process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )
            
            # Initialize the connection
            init_request = {
                "jsonrpc": "2.0",
                "id": self._next_request_id(),
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {},
                        "resources": {}
                    },
                    "clientInfo": {
                        "name": "cherry-studio-clone",
                        "version": "1.0.0"
                    }
                }
            }
            
            response = await self._send_request(init_request)
            if response and not response.get("error"):
                self.connected = True
                
                # Get available tools and resources
                await self._discover_capabilities()
                
                logger.info(f"Connected to MCP server {self.server.name}")
                return True
            else:
                logger.error(f"Failed to initialize MCP server {self.server.name}: {response}")
                return False
                
        except Exception as e:
            logger.error(f"Error connecting to STDIO MCP server {self.server.name}: {e}")
            return False
    
    async def _connect_http(self) -> bool:
        """Connect to MCP server via HTTP."""
        # HTTP MCP implementation would go here
        logger.warning("HTTP MCP transport not yet implemented")
        return False
    
    async def _connect_websocket(self) -> bool:
        """Connect to MCP server via WebSocket."""
        # WebSocket MCP implementation would go here
        logger.warning("WebSocket MCP transport not yet implemented")
        return False
    
    async def _discover_capabilities(self):
        """Discover tools and resources from the server."""
        try:
            # Get tools
            tools_request = {
                "jsonrpc": "2.0",
                "id": self._next_request_id(),
                "method": "tools/list"
            }
            
            tools_response = await self._send_request(tools_request)
            if tools_response and "result" in tools_response:
                tools_data = tools_response["result"].get("tools", [])
                self.tools = [MCPTool.from_dict(tool_data, self.server.name) for tool_data in tools_data]
                logger.info(f"Discovered {len(self.tools)} tools from {self.server.name}")
            
            # Get resources
            resources_request = {
                "jsonrpc": "2.0",
                "id": self._next_request_id(),
                "method": "resources/list"
            }
            
            resources_response = await self._send_request(resources_request)
            if resources_response and "result" in resources_response:
                resources_data = resources_response["result"].get("resources", [])
                self.resources = [MCPResource.from_dict(res_data, self.server.name) for res_data in resources_data]
                logger.info(f"Discovered {len(self.resources)} resources from {self.server.name}")
                
        except Exception as e:
            logger.error(f"Error discovering capabilities from {self.server.name}: {e}")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> MCPToolResult:
        """Call a tool on the MCP server."""
        if not self.connected:
            return MCPToolResult("", [{"type": "text", "text": "Not connected to MCP server"}], True)
        
        try:
            request = {
                "jsonrpc": "2.0",
                "id": self._next_request_id(),
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            response = await self._send_request(request)
            
            if response and "result" in response:
                return MCPToolResult.from_dict(response["result"], str(request["id"]))
            elif response and "error" in response:
                return MCPToolResult(
                    str(request["id"]),
                    [{"type": "text", "text": f"Tool error: {response['error']}"}],
                    True
                )
            else:
                return MCPToolResult(
                    str(request["id"]),
                    [{"type": "text", "text": "No response from server"}],
                    True
                )
                
        except Exception as e:
            logger.error(f"Error calling tool {tool_name}: {e}")
            return MCPToolResult("", [{"type": "text", "text": f"Tool call error: {e}"}], True)
    
    async def get_resource(self, uri: str) -> Optional[MCPResourceContent]:
        """Get a resource from the MCP server."""
        if not self.connected:
            return None
        
        try:
            request = {
                "jsonrpc": "2.0",
                "id": self._next_request_id(),
                "method": "resources/read",
                "params": {
                    "uri": uri
                }
            }
            
            response = await self._send_request(request)
            
            if response and "result" in response:
                return MCPResourceContent.from_dict(response["result"])
            else:
                logger.error(f"Failed to get resource {uri}: {response}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting resource {uri}: {e}")
            return None
    
    async def _send_request(self, request: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Send a JSON-RPC request to the server."""
        if not self.process or self.process.poll() is not None:
            logger.error("MCP server process is not running")
            return None
        
        try:
            # Send request
            request_json = json.dumps(request) + "\n"
            self.process.stdin.write(request_json)
            self.process.stdin.flush()
            
            # Read response
            response_line = self.process.stdout.readline()
            if not response_line:
                logger.error("No response from MCP server")
                return None
            
            response = json.loads(response_line.strip())
            return response
            
        except Exception as e:
            logger.error(f"Error sending request to MCP server: {e}")
            return None
    
    def _next_request_id(self) -> int:
        """Get the next request ID."""
        self.request_id += 1
        return self.request_id
    
    async def disconnect(self):
        """Disconnect from the MCP server."""
        try:
            if self.connected and self.process:
                # Send shutdown notification
                shutdown_request = {
                    "jsonrpc": "2.0",
                    "method": "notifications/shutdown"
                }
                
                try:
                    request_json = json.dumps(shutdown_request) + "\n"
                    self.process.stdin.write(request_json)
                    self.process.stdin.flush()
                except:
                    pass  # Ignore errors during shutdown
                
                # Terminate the process
                self.process.terminate()
                
                # Wait for process to end
                try:
                    await asyncio.wait_for(
                        asyncio.create_task(self._wait_for_process()),
                        timeout=5.0
                    )
                except asyncio.TimeoutError:
                    # Force kill if it doesn't terminate gracefully
                    self.process.kill()
                    await asyncio.create_task(self._wait_for_process())
            
            self.connected = False
            self.process = None
            logger.info(f"Disconnected from MCP server {self.server.name}")
            
        except Exception as e:
            logger.error(f"Error disconnecting from MCP server {self.server.name}: {e}")
    
    async def _wait_for_process(self):
        """Wait for the process to terminate."""
        if self.process:
            while self.process.poll() is None:
                await asyncio.sleep(0.1)
    
    def is_connected(self) -> bool:
        """Check if connected to the server."""
        return self.connected and self.process and self.process.poll() is None
    
    def get_tools(self) -> List[MCPTool]:
        """Get available tools from this server."""
        return self.tools
    
    def get_resources(self) -> List[MCPResource]:
        """Get available resources from this server."""
        return self.resources
    
    def get_tool(self, tool_name: str) -> Optional[MCPTool]:
        """Get a specific tool by name."""
        for tool in self.tools:
            if tool.name == tool_name:
                return tool
        return None
    
    def get_resource(self, uri: str) -> Optional[MCPResource]:
        """Get a specific resource by URI."""
        for resource in self.resources:
            if resource.uri == uri:
                return resource
        return None
