import json
import os
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class CategoryManager:
    """Manages conversation categories and organization."""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = data_dir
        self.categories_file = os.path.join(data_dir, "categories.json")
        self.conversations_file = os.path.join(data_dir, "conversations.json")
        self.categories: Dict[str, Dict[str, Any]] = {}
        self.conversations: Dict[str, Dict[str, Any]] = {}
        self.load_data()
    
    def load_data(self):
        """Load categories and conversations from JSON files."""
        # Load categories
        if os.path.exists(self.categories_file):
            try:
                with open(self.categories_file, 'r', encoding='utf-8') as f:
                    self.categories = json.load(f)
            except Exception as e:
                logger.error(f"Error loading categories: {e}")
                self.categories = {}
        
        # Load conversations
        if os.path.exists(self.conversations_file):
            try:
                with open(self.conversations_file, 'r', encoding='utf-8') as f:
                    self.conversations = json.load(f)
            except Exception as e:
                logger.error(f"Error loading conversations: {e}")
                self.conversations = {}
        
        # Ensure default category exists
        if "uncategorized" not in self.categories:
            self.create_category("uncategorized", "Uncategorized", is_default=True)
        
        # Migrate existing conversations to categories
        self.migrate_existing_conversations()
    
    def save_data(self):
        """Save categories and conversations to JSON files."""
        os.makedirs(self.data_dir, exist_ok=True)
        
        try:
            with open(self.categories_file, 'w', encoding='utf-8') as f:
                json.dump(self.categories, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving categories: {e}")
        
        try:
            with open(self.conversations_file, 'w', encoding='utf-8') as f:
                json.dump(self.conversations, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving conversations: {e}")
    
    def migrate_existing_conversations(self):
        """Migrate existing conversations to use categories."""
        updated = False
        
        for conv_id, conv_data in self.conversations.items():
            if 'category_id' not in conv_data:
                # Determine category based on conversation title/content
                category_id = self._determine_category_for_conversation(conv_data)
                conv_data['category_id'] = category_id
                updated = True
        
        if updated:
            self.save_data()
    
    def _determine_category_for_conversation(self, conv_data: Dict[str, Any]) -> str:
        """Determine appropriate category for a conversation based on content."""
        title = conv_data.get('title', '').lower()
        messages = conv_data.get('messages', [])
        
        # Simple keyword-based categorization
        if any(keyword in title for keyword in ['code', 'programming', 'python', 'javascript', 'debug']):
            return 'development'
        elif any(keyword in title for keyword in ['design', 'ui', 'ux', 'figma', 'mockup']):
            return 'design'
        elif any(keyword in title for keyword in ['meeting', 'planning', 'project', 'task']):
            return 'planning'
        elif any(keyword in title for keyword in ['research', 'learning', 'study', 'tutorial']):
            return 'learning'
        else:
            return 'uncategorized'
    
    def create_category(self, category_id: str, name: str, parent_id: Optional[str] = None, 
                       color: str = "#6B7280", is_default: bool = False) -> bool:
        """Create a new category."""
        if category_id in self.categories:
            return False
        
        self.categories[category_id] = {
            'id': category_id,
            'name': name,
            'parent_id': parent_id,
            'color': color,
            'is_default': is_default,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        self.save_data()
        return True
    
    def delete_category(self, category_id: str, move_to: str = "uncategorized") -> bool:
        """Delete a category and move conversations to another category."""
        if category_id not in self.categories or self.categories[category_id].get('is_default', False):
            return False
        
        # Move all conversations to target category
        for conv_id, conv_data in self.conversations.items():
            if conv_data.get('category_id') == category_id:
                conv_data['category_id'] = move_to
        
        del self.categories[category_id]
        self.save_data()
        return True
    
    def rename_category(self, category_id: str, new_name: str) -> bool:
        """Rename a category."""
        if category_id not in self.categories:
            return False
        
        self.categories[category_id]['name'] = new_name
        self.categories[category_id]['updated_at'] = datetime.now().isoformat()
        self.save_data()
        return True
    
    def assign_conversation_to_category(self, conversation_id: str, category_id: str) -> bool:
        """Assign a conversation to a category."""
        if conversation_id not in self.conversations or category_id not in self.categories:
            return False
        
        self.conversations[conversation_id]['category_id'] = category_id
        self.save_data()
        return True
    
    def get_categories(self) -> Dict[str, Dict[str, Any]]:
        """Get all categories."""
        return self.categories
    
    def get_conversations_in_category(self, category_id: str) -> Dict[str, Dict[str, Any]]:
        """Get all conversations in a specific category."""
        return {
            conv_id: conv_data 
            for conv_id, conv_data in self.conversations.items()
            if conv_data.get('category_id') == category_id
        }
    
    def get_category_tree(self) -> Dict[str, List[str]]:
        """Get category tree structure for display."""
        tree = {}
        
        # Group categories by parent
        for cat_id, cat_data in self.categories.items():
            parent_id = cat_data.get('parent_id', None)
            if parent_id not in tree:
                tree[parent_id] = []
            tree[parent_id].append(cat_id)
        
        return tree
    
    def search_conversations(self, query: str) -> Dict[str, Dict[str, Any]]:
        """Search conversations across all categories."""
        query = query.lower()
        results = {}
        
        for conv_id, conv_data in self.conversations.items():
            # Search in title
            if query in conv_data.get('title', '').lower():
                results[conv_id] = conv_data
                continue
            
            # Search in messages
            messages = conv_data.get('messages', [])
            for msg in messages:
                if query in msg.get('content', '').lower():
                    results[conv_id] = conv_data
                    break
        
        return results
