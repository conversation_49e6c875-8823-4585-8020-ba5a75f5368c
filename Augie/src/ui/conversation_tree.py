import tkinter as tk
from tkinter import ttk, simpledialog, messagebox
import uuid
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import logging

from .category_manager import CategoryManager
from .themes import ThePrinteryTheme

logger = logging.getLogger(__name__)

class ConversationTreeView(ttk.Frame):
    """Expandable tree view for conversations organized by categories."""
    
    def __init__(self, parent, category_manager: CategoryManager, 
                 on_conversation_select: Optional[Callable] = None,
                 on_conversation_rename: Optional[Callable] = None,
                 on_conversation_delete: Optional[Callable] = None,
                 **kwargs):
        super().__init__(parent, **kwargs)
        
        self.category_manager = category_manager
        self.on_conversation_select = on_conversation_select
        self.on_conversation_rename = on_conversation_rename
        self.on_conversation_delete = on_conversation_delete
        
        # Theme colors
        self.theme = ThePrinteryTheme()
        
        # Setup tree view
        self.setup_tree()
        self.setup_scrollbars()
        self.setup_context_menu()
        self.setup_drag_drop()
        
        # Load initial data
        self.refresh_tree()
    
    def setup_tree(self):
        """Setup the tree view widget."""
        style = ttk.Style()
        style.configure("Treeview",
                       background=self.theme.bg_secondary,
                       foreground=self.theme.text_primary,
                       fieldbackground=self.theme.bg_secondary,
                       borderwidth=0,
                       relief="flat")
        
        style.configure("Treeview.Heading",
                       background=self.theme.bg_primary,
                       foreground=self.theme.text_primary,
                       relief="flat")
        
        style.map("Treeview",
                 background=[("selected", self.theme.accent)],
                 foreground=[("selected", self.theme.text_primary)])
        
        # Create tree
        self.tree = ttk.Treeview(self, columns=("date", "messages"), show="tree headings", 
                                selectmode="browse")
        self.tree.heading("#0", text="Conversations")
        self.tree.heading("date", text="Last Updated")
        self.tree.heading("messages", text="Messages")
        
        self.tree.column("#0", width=250, minwidth=150)
        self.tree.column("date", width=120, minwidth=80)
        self.tree.column("messages", width=60, minwidth=40)
        
        # Bind events
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)
        self.tree.bind("<Double-1>", self.on_tree_double_click)
        self.tree.bind("<Button-3>", self.on_right_click)
        
        self.tree.pack(side="left", fill="both", expand=True)
    
    def setup_scrollbars(self):
        """Setup scrollbars for the tree."""
        # Vertical scrollbar
        vsb = ttk.Scrollbar(self, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=vsb.set)
        vsb.pack(side="right", fill="y")
    
    def setup_context_menu(self):
        """Setup right-click context menu."""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="Rename", command=self.rename_selected)
        self.context_menu.add_command(label="Delete", command=self.delete_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Move to Category", command=self.move_to_category)
        self.context_menu.add_command(label="New Category", command=self.create_category)
    
    def setup_drag_drop(self):
        """Setup drag and drop functionality."""
        self.drag_data = {"item": None, "category": None}
        
        self.tree.bind("<Button-1>", self.on_drag_start)
        self.tree.bind("<B1-Motion>", self.on_drag_motion)
        self.tree.bind("<ButtonRelease-1>", self.on_drag_end)
    
    def refresh_tree(self):
        """Refresh the tree view with current data."""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Build category tree
        tree_structure = self.category_manager.get_category_tree()
        self.build_category_tree(None, tree_structure)
    
    def build_category_tree(self, parent_id: Optional[str], tree_structure: Dict[str, List[str]]):
        """Build the category tree recursively."""
        categories = tree_structure.get(parent_id, [])
        
        for category_id in categories:
            category = self.category_manager.get_categories()[category_id]
            
            # Create category node
            category_node = self.tree.insert(
                parent_id or "", "end", category_id,
                text=f"📁 {category['name']}",
                values=("", ""),
                tags=("category",)
            )
            
            # Add conversations in this category
            conversations = self.category_manager.get_conversations_in_category(category_id)
            for conv_id, conv_data in conversations.items():
                self.add_conversation_to_tree(conv_id, conv_data, category_node)
            
            # Add subcategories
            self.build_category_tree(category_id, tree_structure)
    
    def add_conversation_to_tree(self, conv_id: str, conv_data: Dict[str, Any], 
                                parent_category: str):
        """Add a conversation to the tree."""
        title = conv_data.get('title', 'Untitled')
        last_updated = conv_data.get('updated_at', conv_data.get('created_at', ''))
        message_count = len(conv_data.get('messages', []))
        
        # Format date
        try:
            date_obj = datetime.fromisoformat(last_updated)
            formatted_date = date_obj.strftime("%m/%d %H:%M")
        except:
            formatted_date = "Unknown"
        
        self.tree.insert(
            parent_category, "end", conv_id,
            text=f"💬 {title}",
            values=(formatted_date, str(message_count)),
            tags=("conversation",)
        )
    
    def on_tree_select(self, event):
        """Handle tree selection."""
        selection = self.tree.selection()
        if selection:
            item_id = selection[0]
            if item_id in self.category_manager.conversations:
                if self.on_conversation_select:
                    self.on_conversation_select(item_id)
    
    def on_tree_double_click(self, event):
        """Handle double-click on tree items."""
        selection = self.tree.selection()
        if selection:
            item_id = selection[0]
            if item_id in self.category_manager.conversations:
                if self.on_conversation_select:
                    self.on_conversation_select(item_id)
            else:
                # Toggle category expansion
                if self.tree.item(item_id, "open"):
                    self.tree.item(item_id, open=False)
                else:
                    self.tree.item(item_id, open=True)
    
    def on_right_click(self, event):
        """Handle right-click context menu."""
        selection = self.tree.identify_row(event.y)
        if selection:
            self.tree.selection_set(selection)
            self.context_menu.post(event.x_root, event.y_root)
    
    def on_drag_start(self, event):
        """Start drag operation."""
        item = self.tree.identify_row(event.y)
        if item and item in self.category_manager.conversations:
            self.drag_data["item"] = item
            self.drag_data["category"] = self.tree.parent(item)
    
    def on_drag_motion(self, event):
        """Handle drag motion."""
        if self.drag_data["item"]:
            item = self.tree.identify_row(event.y)
            if item and item != self.drag_data["item"]:
                # Highlight potential drop target
                self.tree.selection_set(item)
    
    def on_drag_end(self, event):
        """End drag operation and move conversation."""
        if self.drag_data["item"]:
            target_item = self.tree.identify_row(event.y)
            
            if target_item:
                # Determine target category
                if target_item in self.category_manager.conversations:
                    target_category = self.tree.parent(target_item)
                else:
                    target_category = target_item
                
                # Move conversation
                if target_category != self.drag_data["category"]:
                    self.category_manager.assign_conversation_to_category(
                        self.drag_data["item"], target_category
                    )
                    self.refresh_tree()
            
            self.drag_data = {"item": None, "category": None}
    
    def rename_selected(self):
        """Rename selected conversation or category."""
        selection = self.tree.selection()
        if not selection:
            return
        
        item_id = selection[0]
        
        if item_id in self.category_manager.conversations:
            # Rename conversation
            current_title = self.category_manager.conversations[item_id].get('title', '')
            new_title = simpledialog.askstring("Rename Conversation", 
                                             "Enter new title:", 
                                             initialvalue=current_title)
            if new_title and self.on_conversation_rename:
                self.on_conversation_rename(item_id, new_title)
        else:
            # Rename category
            current_name = self.category_manager.categories[item_id]['name']
            new_name = simpledialog.askstring("Rename Category", 
                                            "Enter new name:", 
                                            initialvalue=current_name)
            if new_name:
                self.category_manager.rename_category(item_id, new_name)
                self.refresh_tree()
    
    def delete_selected(self):
        """Delete selected conversation or category."""
        selection = self.tree.selection()
        if not selection:
            return
        
        item_id = selection[0]
        
        if item_id in self.category_manager.conversations:
            # Delete conversation
            if messagebox.askyesno("Delete Conversation", 
                                 "Are you sure you want to delete this conversation?"):
                if self.on_conversation_delete:
                    self.on_conversation_delete(item_id)
        else:
            # Delete category
            if messagebox.askyesno("Delete Category", 
                                 "Are you sure you want to delete this category and move all conversations to Uncategorized?"):
                self.category_manager.delete_category(item_id)
                self.refresh_tree()
    
    def move_to_category(self):
        """Move selected conversation to a different category."""
        selection = self.tree.selection()
        if not selection or selection[0] not in self.category_manager.conversations:
            return
        
        conv_id = selection[0]
        
        # Create category selection dialog
        dialog = tk.Toplevel(self)
        dialog.title("Move to Category")
        dialog.geometry("300x200")
        
        categories = self.category_manager.get_categories()
        category_list = tk.Listbox(dialog)
        category_list.pack(padx=10, pady=10, fill="both", expand=True)
        
        for cat_id, cat_data in categories.items():
            if cat_id != self.category_manager.conversations[conv_id].get('category_id'):
                category_list.insert("end", cat_data['name'])
        
        def move_conversation():
            selection = category_list.curselection()
            if selection:
                selected_name = category_list.get(selection[0])
                for cat_id, cat_data in categories.items():
                    if cat_data['name'] == selected_name:
                        self.category_manager.assign_conversation_to_category(conv_id, cat_id)
                        self.refresh_tree()
                        dialog.destroy()
                        break
        
        tk.Button(dialog, text="Move", command=move_conversation).pack(pady=5)
    
    def create_category(self):
        """Create a new category."""
        dialog = tk.Toplevel(self)
        dialog.title("Create Category")
        dialog.geometry("300x150")
        
        tk.Label(dialog, text="Category Name:").pack(pady=5)
        name_entry = tk.Entry(dialog)
        name_entry.pack(pady=5, padx=10, fill="x")
        
        def create():
            name = name_entry.get().strip()
            if name:
                category_id = name.lower().replace(" ", "_")
                self.category_manager.create_category(category_id, name)
                self.refresh_tree()
                dialog.destroy()
        
        tk.Button(dialog, text="Create", command=create).pack(pady=10)
    
    def select_conversation(self, conversation_id: str):
        """Select a specific conversation in the tree."""
        self.tree.selection_set(conversation_id)
        self.tree.see(conversation_id)
    
    def update_conversation(self, conversation_id: str, conv_data: Dict[str, Any]):
        """Update a conversation in the tree."""
        if self.tree.exists(conversation_id):
            # Update existing item
            title = conv_data.get('title', 'Untitled')
            last_updated = conv_data.get('updated_at', conv_data.get('created_at', ''))
            message_count = len(conv_data.get('messages', []))
            
            try:
                date_obj = datetime.fromisoformat(last_updated)
                formatted_date = date_obj.strftime("%m/%d %H:%M")
            except:
                formatted_date = "Unknown"
            
            self.tree.item(conversation_id, text=f"💬 {title}", 
                          values=(formatted_date, str(message_count)))
        else:
            # Add new conversation
            category_id = conv_data.get('category_id', 'uncategorized')
            parent_category = category_id if self.tree.exists(category_id) else ""
            self.add_conversation_to_tree(conversation_id, conv_data, parent_category)
    
    def remove_conversation(self, conversation_id: str):
        """Remove a conversation from the tree."""
        if self.tree.exists(conversation_id):
            self.tree.delete(conversation_id)
