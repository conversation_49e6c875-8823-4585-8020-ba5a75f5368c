"""
MCP Manager window for configuring and monitoring MCP servers and tools.
"""
import tkinter as tk
from tkinter import ttk, messagebox
import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

from ..mcp import MCPServerManager, MCPServer, MCPTransport
from ..mcp.types import MCPToolInputType

logger = logging.getLogger(__name__)

class MCPManagerWindow(tk.Toplevel):
    """Window for managing MCP servers and tools."""
    
    def __init__(self, parent, config):
        super().__init__(parent)
        self.parent = parent
        self.config = config
        
        # Window configuration
        self.title("🔧 MCP Server Manager")
        self.geometry("900x700")
        self.minsize(700, 500)
        
        # Initialize MCP manager
        self.mcp_manager = MCPServerManager()
        
        # Set up UI
        self.setup_ui()
        
        # Load initial data
        self.refresh_servers()
        
        # Start local tools and auto-start servers
        if hasattr(parent, 'schedule_task'):
            parent.schedule_task(self.initialize_servers())
    
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(
            main_frame,
            text="🔧 MCP Server Manager",
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # Create notebook for different views
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Servers tab
        self.setup_servers_tab()
        
        # Tools tab
        self.setup_tools_tab()
        
        # Local tools tab
        self.setup_local_tools_tab()
        
        # Control buttons
        self.setup_control_buttons(main_frame)
    
    def setup_servers_tab(self):
        """Set up the servers management tab."""
        servers_frame = ttk.Frame(self.notebook)
        self.notebook.add(servers_frame, text="Servers")
        
        # Toolbar
        toolbar = ttk.Frame(servers_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(
            toolbar,
            text="➕ Add Server",
            command=self.add_server_dialog
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            toolbar,
            text="✏️ Edit Server",
            command=self.edit_server_dialog
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            toolbar,
            text="🗑️ Remove Server",
            command=self.remove_server
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            toolbar,
            text="🔄 Refresh",
            command=self.refresh_servers
        ).pack(side=tk.RIGHT)
        
        # Servers treeview
        columns = ('Name', 'Status', 'Type', 'Tools', 'Auto Start', 'Description')
        self.servers_tree = ttk.Treeview(servers_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.servers_tree.heading('Name', text='Server Name')
        self.servers_tree.heading('Status', text='Status')
        self.servers_tree.heading('Type', text='Transport')
        self.servers_tree.heading('Tools', text='Tools')
        self.servers_tree.heading('Auto Start', text='Auto Start')
        self.servers_tree.heading('Description', text='Description')
        
        self.servers_tree.column('Name', width=150)
        self.servers_tree.column('Status', width=100)
        self.servers_tree.column('Type', width=80)
        self.servers_tree.column('Tools', width=60)
        self.servers_tree.column('Auto Start', width=80)
        self.servers_tree.column('Description', width=300)
        
        # Scrollbar for servers treeview
        servers_scrollbar = ttk.Scrollbar(servers_frame, orient=tk.VERTICAL, command=self.servers_tree.yview)
        self.servers_tree.configure(yscrollcommand=servers_scrollbar.set)
        
        # Pack treeview and scrollbar
        self.servers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        servers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind double-click to edit
        self.servers_tree.bind('<Double-1>', lambda e: self.edit_server_dialog())
    
    def setup_tools_tab(self):
        """Set up the tools overview tab."""
        tools_frame = ttk.Frame(self.notebook)
        self.notebook.add(tools_frame, text="All Tools")
        
        # Toolbar
        toolbar = ttk.Frame(tools_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(
            toolbar,
            text="🔄 Refresh Tools",
            command=self.refresh_tools
        ).pack(side=tk.LEFT)
        
        ttk.Button(
            toolbar,
            text="🧪 Test Tool",
            command=self.test_tool_dialog
        ).pack(side=tk.LEFT, padx=(5, 0))
        
        # Tools treeview
        tool_columns = ('Server', 'Tool Name', 'Description', 'Parameters')
        self.tools_tree = ttk.Treeview(tools_frame, columns=tool_columns, show='headings', height=20)
        
        # Configure columns
        self.tools_tree.heading('Server', text='Server')
        self.tools_tree.heading('Tool Name', text='Tool Name')
        self.tools_tree.heading('Description', text='Description')
        self.tools_tree.heading('Parameters', text='Parameters')
        
        self.tools_tree.column('Server', width=150)
        self.tools_tree.column('Tool Name', width=200)
        self.tools_tree.column('Description', width=300)
        self.tools_tree.column('Parameters', width=200)
        
        # Scrollbar for tools treeview
        tools_scrollbar = ttk.Scrollbar(tools_frame, orient=tk.VERTICAL, command=self.tools_tree.yview)
        self.tools_tree.configure(yscrollcommand=tools_scrollbar.set)
        
        # Pack treeview and scrollbar
        self.tools_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tools_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_local_tools_tab(self):
        """Set up the local tools tab."""
        local_frame = ttk.Frame(self.notebook)
        self.notebook.add(local_frame, text="Local Tools")
        
        # Info text
        info_text = tk.Text(
            local_frame,
            wrap=tk.WORD,
            font=('Consolas', 10),
            state=tk.DISABLED,
            height=25
        )
        
        # Scrollbar for info text
        info_scrollbar = ttk.Scrollbar(local_frame, orient=tk.VERTICAL, command=info_text.yview)
        info_text.configure(yscrollcommand=info_scrollbar.set)
        
        # Pack text widget and scrollbar
        info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Populate local tools info
        self.populate_local_tools_info(info_text)
    
    def setup_control_buttons(self, parent):
        """Set up control buttons."""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(
            button_frame,
            text="🚀 Start All",
            command=self.start_all_servers
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            button_frame,
            text="⏹️ Stop All",
            command=self.stop_all_servers
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            button_frame,
            text="🔄 Refresh All",
            command=self.refresh_all
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            button_frame,
            text="❌ Close",
            command=self.destroy
        ).pack(side=tk.RIGHT)
    
    def populate_local_tools_info(self, text_widget):
        """Populate the local tools information."""
        text_widget.config(state=tk.NORMAL)
        text_widget.delete(1.0, tk.END)
        
        info_text = "🛠️ Built-in Local Tools\n"
        info_text += "=" * 50 + "\n\n"
        info_text += "These tools are always available and run locally for security and performance.\n\n"
        
        tools = self.mcp_manager.local_tools_server.get_tools()
        
        for tool in tools:
            info_text += f"🔧 {tool.name}\n"
            info_text += f"   Description: {tool.description}\n"
            
            if tool.parameters:
                info_text += "   Parameters:\n"
                for param in tool.parameters:
                    required_text = " (required)" if param.required else ""
                    default_text = f" [default: {param.default}]" if param.default is not None else ""
                    info_text += f"     • {param.name} ({param.type.value}){required_text}{default_text}\n"
                    info_text += f"       {param.description}\n"
            else:
                info_text += "   Parameters: None\n"
            
            info_text += "\n"
        
        info_text += "\n💡 Usage Examples:\n"
        info_text += "=" * 20 + "\n"
        info_text += "• read_file: Read any text file on the system\n"
        info_text += "• write_file: Create or modify files\n"
        info_text += "• list_directory: Browse file system\n"
        info_text += "• web_request: Make HTTP requests to APIs\n"
        info_text += "• execute_command: Run system commands safely\n"
        info_text += "• calculate: Perform mathematical calculations\n"
        info_text += "• search_text: Search for patterns in text\n"
        
        text_widget.insert(1.0, info_text)
        text_widget.config(state=tk.DISABLED)
    
    async def initialize_servers(self):
        """Initialize MCP servers."""
        try:
            await self.mcp_manager.start_all_servers()
            self.refresh_servers()
            self.refresh_tools()
        except Exception as e:
            logger.error(f"Error initializing MCP servers: {e}")
    
    def refresh_servers(self):
        """Refresh the servers display."""
        # Clear existing items
        for item in self.servers_tree.get_children():
            self.servers_tree.delete(item)
        
        # Get server status
        server_status = self.mcp_manager.get_server_status()
        
        # Add servers to treeview
        for server_name, status in server_status.items():
            status_text = "🟢 Connected" if status["connected"] else "🔴 Disconnected"
            if server_name in self.mcp_manager.servers:
                server = self.mcp_manager.servers[server_name]
                auto_start_text = "✅" if server.auto_start else "❌"
                transport_text = server.transport.value
                description = server.description
            else:
                # Local tools server
                auto_start_text = "✅"
                transport_text = "local"
                description = "Built-in local tools"
            
            self.servers_tree.insert('', 'end', values=(
                status["name"],
                status_text,
                transport_text,
                str(status["tools_count"]),
                auto_start_text,
                description
            ))
    
    def refresh_tools(self):
        """Refresh the tools display."""
        # Clear existing items
        for item in self.tools_tree.get_children():
            self.tools_tree.delete(item)
        
        # Get tools by server
        tools_by_server = self.mcp_manager.get_tools_by_server()
        
        # Add tools to treeview
        for server_name, tools in tools_by_server.items():
            server_display = "Local Tools" if server_name == "local_tools" else server_name
            
            for tool in tools:
                param_names = [param.name for param in tool.parameters]
                params_text = ", ".join(param_names) if param_names else "None"
                
                self.tools_tree.insert('', 'end', values=(
                    server_display,
                    tool.name,
                    tool.description,
                    params_text
                ))
    
    def add_server_dialog(self):
        """Show dialog to add a new MCP server."""
        dialog = MCPServerDialog(self, "Add MCP Server")
        self.wait_window(dialog)
        
        if dialog.result:
            server = dialog.result
            if self.mcp_manager.add_server(server):
                self.refresh_servers()
                messagebox.showinfo("Success", f"Added MCP server: {server.display_name}")
            else:
                messagebox.showerror("Error", "Failed to add MCP server")
    
    def edit_server_dialog(self):
        """Show dialog to edit an MCP server."""
        selection = self.servers_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a server to edit")
            return
        
        item = selection[0]
        server_name = self.servers_tree.item(item)['values'][0]
        
        # Don't allow editing local tools
        if server_name == "Local Tools":
            messagebox.showinfo("Info", "Local tools server cannot be edited")
            return
        
        # Find the actual server name
        actual_server_name = None
        for name, server in self.mcp_manager.servers.items():
            if server.display_name == server_name:
                actual_server_name = name
                break
        
        if not actual_server_name:
            messagebox.showerror("Error", "Server not found")
            return
        
        server = self.mcp_manager.servers[actual_server_name]
        dialog = MCPServerDialog(self, "Edit MCP Server", server)
        self.wait_window(dialog)
        
        if dialog.result:
            updated_server = dialog.result
            self.mcp_manager.servers[actual_server_name] = updated_server
            self.mcp_manager.save_servers()
            self.refresh_servers()
            messagebox.showinfo("Success", f"Updated MCP server: {updated_server.display_name}")
    
    def remove_server(self):
        """Remove the selected MCP server."""
        selection = self.servers_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a server to remove")
            return
        
        item = selection[0]
        server_name = self.servers_tree.item(item)['values'][0]
        
        # Don't allow removing local tools
        if server_name == "Local Tools":
            messagebox.showinfo("Info", "Local tools server cannot be removed")
            return
        
        # Confirm removal
        if messagebox.askyesno("Confirm", f"Remove MCP server '{server_name}'?"):
            # Find the actual server name
            actual_server_name = None
            for name, server in self.mcp_manager.servers.items():
                if server.display_name == server_name:
                    actual_server_name = name
                    break
            
            if actual_server_name and self.mcp_manager.remove_server(actual_server_name):
                self.refresh_servers()
                messagebox.showinfo("Success", f"Removed MCP server: {server_name}")
            else:
                messagebox.showerror("Error", "Failed to remove MCP server")
    
    def test_tool_dialog(self):
        """Show dialog to test a tool."""
        selection = self.tools_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a tool to test")
            return
        
        item = selection[0]
        values = self.tools_tree.item(item)['values']
        server_name = values[0]
        tool_name = values[1]
        
        # Get the tool
        tool = self.mcp_manager.get_tool(tool_name)
        if not tool:
            messagebox.showerror("Error", f"Tool not found: {tool_name}")
            return
        
        dialog = ToolTestDialog(self, tool)
        self.wait_window(dialog)
        
        if dialog.result is not None:
            # Execute the tool
            if hasattr(self.parent, 'schedule_task'):
                self.parent.schedule_task(self.execute_tool_test(tool_name, dialog.result))
    
    async def execute_tool_test(self, tool_name: str, arguments: Dict[str, Any]):
        """Execute a tool test."""
        try:
            result = await self.mcp_manager.call_tool(tool_name, arguments)
            
            # Show result in a dialog
            result_text = ""
            for content_item in result.content:
                if content_item.get("type") == "text":
                    result_text += content_item.get("text", "") + "\n"
            
            self.after(0, lambda: self.show_tool_result(tool_name, result_text, result.is_error))
            
        except Exception as e:
            error_text = f"Tool execution error: {e}"
            self.after(0, lambda: self.show_tool_result(tool_name, error_text, True))
    
    def show_tool_result(self, tool_name: str, result_text: str, is_error: bool):
        """Show tool execution result."""
        title = f"Tool Result: {tool_name}"
        if is_error:
            messagebox.showerror(title, result_text)
        else:
            messagebox.showinfo(title, result_text)
    
    def start_all_servers(self):
        """Start all enabled servers."""
        if hasattr(self.parent, 'schedule_task'):
            self.parent.schedule_task(self.mcp_manager.start_all_servers())
            self.after(1000, self.refresh_servers)  # Refresh after a delay
    
    def stop_all_servers(self):
        """Stop all servers."""
        if hasattr(self.parent, 'schedule_task'):
            self.parent.schedule_task(self.mcp_manager.stop_all_servers())
            self.after(1000, self.refresh_servers)  # Refresh after a delay
    
    def refresh_all(self):
        """Refresh all displays."""
        self.refresh_servers()
        self.refresh_tools()

class MCPServerDialog(tk.Toplevel):
    """Dialog for adding/editing MCP servers."""
    
    def __init__(self, parent, title: str, server: Optional[MCPServer] = None):
        super().__init__(parent)
        self.parent = parent
        self.server = server
        self.result: Optional[MCPServer] = None
        
        self.title(title)
        self.geometry("500x600")
        self.resizable(False, False)
        
        # Make dialog modal
        self.transient(parent)
        self.grab_set()
        
        self.setup_ui()
        
        if server:
            self.populate_fields()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        main_frame = ttk.Frame(self, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Server name
        ttk.Label(main_frame, text="Server Name:").pack(anchor=tk.W)
        self.name_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.name_var, width=50).pack(fill=tk.X, pady=(0, 10))
        
        # Display name
        ttk.Label(main_frame, text="Display Name:").pack(anchor=tk.W)
        self.display_name_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.display_name_var, width=50).pack(fill=tk.X, pady=(0, 10))
        
        # Description
        ttk.Label(main_frame, text="Description:").pack(anchor=tk.W)
        self.description_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.description_var, width=50).pack(fill=tk.X, pady=(0, 10))
        
        # Transport
        ttk.Label(main_frame, text="Transport:").pack(anchor=tk.W)
        self.transport_var = tk.StringVar(value="stdio")
        transport_combo = ttk.Combobox(
            main_frame, 
            textvariable=self.transport_var,
            values=["stdio", "http", "websocket"],
            state="readonly"
        )
        transport_combo.pack(fill=tk.X, pady=(0, 10))
        
        # Command
        ttk.Label(main_frame, text="Command (space-separated):").pack(anchor=tk.W)
        self.command_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.command_var, width=50).pack(fill=tk.X, pady=(0, 10))
        
        # Arguments
        ttk.Label(main_frame, text="Arguments (space-separated):").pack(anchor=tk.W)
        self.args_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.args_var, width=50).pack(fill=tk.X, pady=(0, 10))
        
        # Environment variables
        ttk.Label(main_frame, text="Environment Variables (JSON):").pack(anchor=tk.W)
        self.env_text = tk.Text(main_frame, height=4, width=50)
        self.env_text.pack(fill=tk.X, pady=(0, 10))
        
        # Checkboxes
        checkbox_frame = ttk.Frame(main_frame)
        checkbox_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(checkbox_frame, text="Enabled", variable=self.enabled_var).pack(side=tk.LEFT)
        
        self.auto_start_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(checkbox_frame, text="Auto Start", variable=self.auto_start_var).pack(side=tk.LEFT, padx=(20, 0))
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="Save", command=self.save_server).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=self.destroy).pack(side=tk.RIGHT)
    
    def populate_fields(self):
        """Populate fields with existing server data."""
        if not self.server:
            return
        
        self.name_var.set(self.server.name)
        self.display_name_var.set(self.server.display_name)
        self.description_var.set(self.server.description)
        self.transport_var.set(self.server.transport.value)
        
        if self.server.command:
            self.command_var.set(" ".join(self.server.command))
        
        if self.server.args:
            self.args_var.set(" ".join(self.server.args))
        
        if self.server.env:
            self.env_text.insert(1.0, json.dumps(self.server.env, indent=2))
        
        self.enabled_var.set(self.server.enabled)
        self.auto_start_var.set(self.server.auto_start)
    
    def save_server(self):
        """Save the server configuration."""
        try:
            # Validate required fields
            if not self.name_var.get().strip():
                messagebox.showerror("Error", "Server name is required")
                return
            
            if not self.display_name_var.get().strip():
                messagebox.showerror("Error", "Display name is required")
                return
            
            # Parse command
            command = self.command_var.get().strip().split() if self.command_var.get().strip() else None
            
            # Parse arguments
            args = self.args_var.get().strip().split() if self.args_var.get().strip() else None
            
            # Parse environment variables
            env = None
            env_text = self.env_text.get(1.0, tk.END).strip()
            if env_text:
                try:
                    env = json.loads(env_text)
                except json.JSONDecodeError:
                    messagebox.showerror("Error", "Invalid JSON in environment variables")
                    return
            
            # Create server object
            self.result = MCPServer(
                name=self.name_var.get().strip(),
                display_name=self.display_name_var.get().strip(),
                description=self.description_var.get().strip(),
                transport=MCPTransport(self.transport_var.get()),
                command=command,
                args=args,
                env=env,
                enabled=self.enabled_var.get(),
                auto_start=self.auto_start_var.get()
            )
            
            self.destroy()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save server: {e}")

class ToolTestDialog(tk.Toplevel):
    """Dialog for testing MCP tools."""
    
    def __init__(self, parent, tool):
        super().__init__(parent)
        self.parent = parent
        self.tool = tool
        self.result: Optional[Dict[str, Any]] = None
        
        self.title(f"Test Tool: {tool.name}")
        self.geometry("400x500")
        self.resizable(False, False)
        
        # Make dialog modal
        self.transient(parent)
        self.grab_set()
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        main_frame = ttk.Frame(self, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Tool info
        ttk.Label(main_frame, text=f"Tool: {self.tool.name}", font=('Arial', 12, 'bold')).pack(anchor=tk.W)
        ttk.Label(main_frame, text=self.tool.description, wraplength=350).pack(anchor=tk.W, pady=(0, 20))
        
        # Parameters
        self.param_vars = {}
        
        if self.tool.parameters:
            ttk.Label(main_frame, text="Parameters:", font=('Arial', 10, 'bold')).pack(anchor=tk.W)
            
            for param in self.tool.parameters:
                param_frame = ttk.Frame(main_frame)
                param_frame.pack(fill=tk.X, pady=5)
                
                # Parameter label
                label_text = f"{param.name} ({param.type.value})"
                if param.required:
                    label_text += " *"
                
                ttk.Label(param_frame, text=label_text).pack(anchor=tk.W)
                ttk.Label(param_frame, text=param.description, font=('Arial', 8)).pack(anchor=tk.W)
                
                # Parameter input
                if param.type == MCPToolInputType.BOOLEAN:
                    var = tk.BooleanVar(value=param.default if param.default is not None else False)
                    ttk.Checkbutton(param_frame, variable=var).pack(anchor=tk.W)
                elif param.enum:
                    var = tk.StringVar(value=param.default if param.default is not None else param.enum[0])
                    ttk.Combobox(param_frame, textvariable=var, values=param.enum, state="readonly").pack(fill=tk.X)
                else:
                    var = tk.StringVar(value=str(param.default) if param.default is not None else "")
                    ttk.Entry(param_frame, textvariable=var).pack(fill=tk.X)
                
                self.param_vars[param.name] = (var, param)
        else:
            ttk.Label(main_frame, text="No parameters required").pack(anchor=tk.W, pady=(0, 20))
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="Execute", command=self.execute_tool).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=self.destroy).pack(side=tk.RIGHT)
    
    def execute_tool(self):
        """Execute the tool with the provided parameters."""
        try:
            arguments = {}
            
            # Collect parameter values
            for param_name, (var, param) in self.param_vars.items():
                value = var.get()
                
                # Validate required parameters
                if param.required and not value:
                    messagebox.showerror("Error", f"Parameter '{param_name}' is required")
                    return
                
                # Convert value to appropriate type
                if param.type == MCPToolInputType.BOOLEAN:
                    arguments[param_name] = bool(value)
                elif param.type == MCPToolInputType.INTEGER:
                    try:
                        arguments[param_name] = int(value) if value else 0
                    except ValueError:
                        messagebox.showerror("Error", f"Parameter '{param_name}' must be an integer")
                        return
                elif param.type == MCPToolInputType.NUMBER:
                    try:
                        arguments[param_name] = float(value) if value else 0.0
                    except ValueError:
                        messagebox.showerror("Error", f"Parameter '{param_name}' must be a number")
                        return
                elif param.type == MCPToolInputType.OBJECT:
                    try:
                        arguments[param_name] = json.loads(value) if value else {}
                    except json.JSONDecodeError:
                        messagebox.showerror("Error", f"Parameter '{param_name}' must be valid JSON")
                        return
                else:
                    arguments[param_name] = str(value)
            
            self.result = arguments
            self.destroy()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to prepare tool execution: {e}")
