import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import datetime
import json
import os
from typing import List, Dict, Any, Optional
from .themes import ThePrinteryTheme
from .conversation_tree import ConversationTreeView
from .category_manager import CategoryManager

class Message:
    def __init__(self, content: str, role: str = "user", timestamp: datetime.datetime = None):
        self.content = content
        self.role = role
        self.timestamp = timestamp or datetime.datetime.now()

class Topic:
    def __init__(self, title: str, messages: List[Message] = None, created_at: datetime.datetime = None):
        self.title = title
        self.messages = messages or []
        self.created_at = created_at or datetime.datetime.now()

class ModernChatWindowV2(tk.Toplevel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.title("The Printery AI Assistant")
        self.geometry("1400x900")
        self.configure(bg='#1a1a1a')
        
        # Initialize theme
        self.theme = ThePrinteryTheme()
        self.colors = self.theme.colors
        
        # Initialize data
        self.topics = []
        self.current_topic = None
        self.current_conversation_id = None
        
        # Initialize category manager
        self.category_manager = CategoryManager()
        
        # Load existing data
        self.load_data()
        
        # Setup UI
        self.setup_ui()
        
        # Bind window close
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def setup_ui(self):
        """Set up the main UI structure."""
        # Main container
        self.main_container = tk.Frame(self, bg=self.colors['bg_primary'])
        self.main_container.pack(fill=tk.BOTH, expand=True)
        
        # Left sidebar with conversation tree
        self.setup_left_sidebar()
        
        # Main content area
        self.setup_content_area()
        
        # Right sidebar
        self.setup_right_sidebar()
        
    def setup_left_sidebar(self):
        """Set up the left sidebar with conversation tree."""
        self.left_frame = tk.Frame(
            self.main_container,
            bg=self.colors['bg_secondary'],
            width=300
        )
        self.left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=0, pady=0)
        self.left_frame.pack_propagate(False)
        
        # Header
        header_frame = tk.Frame(
            self.left_frame,
            bg=self.colors['accent'],
            height=60
        )
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(
            header_frame,
            text="Conversations",
            font=('Arial', 14, 'bold'),
            bg=self.colors['accent'],
            fg='white'
        ).pack(pady=20)
        
        # Conversation tree
        self.conversation_tree = ConversationTreeView(
            self.left_frame,
            self.colors,
            on_conversation_select=self.on_conversation_select,
            on_conversation_rename=self.on_conversation_rename,
            on_conversation_delete=self.on_conversation_delete
        )
        self.conversation_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # New conversation button
        new_conv_frame = tk.Frame(self.left_frame, bg=self.colors['bg_secondary'])
        new_conv_frame.pack(fill=tk.X, padx=10, pady=10)
        
        new_conv_btn = tk.Button(
            new_conv_frame,
            text="+ New Conversation",
            bg=self.colors['accent'],
            fg='white',
            font=('Arial', 10, 'bold'),
            command=self.create_new_conversation,
            cursor='hand2'
        )
        new_conv_btn.pack(fill=tk.X)
        
    def setup_content_area(self):
        """Set up the main content area with chat interface."""
        self.content_frame = tk.Frame(self.main_container, bg=self.colors['bg_primary'])
        self.content_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Header bar
        self.setup_header_bar()
        
        # Chat content
        self.setup_chat_content()
        
        # Input area
        self.setup_input_area()
        
    def setup_header_bar(self):
        """Set up the header bar with model info and search."""
        self.header_frame = tk.Frame(
            self.content_frame,
            bg=self.colors['bg_primary'],
            height=60
        )
        self.header_frame.pack(fill=tk.X, padx=20, pady=(10, 0))
        self.header_frame.pack_propagate(False)
        
        # Left side (model info)
        left_frame = tk.Frame(self.header_frame, bg=self.colors['bg_primary'])
        left_frame.pack(side=tk.LEFT, fill=tk.Y)
        
        # Model info with icon
        model_frame = tk.Frame(left_frame, bg=self.colors['bg_primary'])
        model_frame.pack(side=tk.LEFT, fill=tk.Y)
        
        model_icon = tk.Label(
            model_frame,
            text="🧠",
            font=('Arial', 16),
            bg=self.colors['accent'],
            fg='white',
            width=2,
            height=1
        )
        model_icon.pack(side=tk.LEFT, padx=(0, 10))
        
        model_details = tk.Frame(model_frame, bg=self.colors['bg_primary'])
        model_details.pack(side=tk.LEFT, fill=tk.Y)
        
        self.model_label = tk.Label(
            model_details,
            text="deepseek-ai/DeepSeek-V3 | SiliconFlow",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            anchor=tk.W
        )
        self.model_label.pack(anchor=tk.W)
        
        self.assistant_label = tk.Label(
            model_details,
            text="Hello, I'm Default Assistant. You can start chatting with me right away",
            font=('Arial', 10),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary'],
            anchor=tk.W
        )
        self.assistant_label.pack(anchor=tk.W)
        
        # Right side (search and update button)
        right_frame = tk.Frame(self.header_frame, bg=self.colors['bg_primary'])
        right_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Search input
        search_frame = tk.Frame(right_frame, bg=self.colors['bg_primary'])
        search_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        self.search_entry = tk.Entry(
            search_frame,
            font=('Arial', 10),
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            relief=tk.SOLID,
            bd=1,
            width=20
        )
        self.search_entry.pack(side=tk.LEFT)
        
        search_btn = tk.Button(
            search_frame,
            text="🔍",
            bg=self.colors['accent'],
            fg='white',
            font=('Arial', 10, 'bold'),
            command=self.perform_search
        )
        search_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # Update available button
        update_btn = tk.Button(
            right_frame,
            text="🔄 Update Available",
            bg=self.colors['warning'],
            fg='black',
            border=0,
            font=('Arial', 9),
            cursor='hand2'
        )
        update_btn.pack(side=tk.LEFT, padx=(10, 0))
        
    def setup_chat_content(self):
        """Set up the main chat content area."""
        # Clear previous content
        for widget in self.content_frame.winfo_children():
            if widget != self.header_frame:
                widget.destroy()
        
        # Chat container
        chat_container = tk.Frame(self.content_frame, bg=self.colors['bg_primary'])
        chat_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Messages area with scrollbar
        self.messages_frame = tk.Frame(chat_container, bg=self.colors['bg_primary'])
        self.messages_frame.pack(fill=tk.BOTH, expand=True)
        
        # Canvas for scrolling
        self.messages_canvas = tk.Canvas(
            self.messages_frame,
            bg=self.colors['bg_primary'],
            highlightthickness=0
        )
        self.messages_scrollbar = ttk.Scrollbar(
            self.messages_frame,
            orient="vertical",
            command=self.messages_canvas.yview
        )
        self.messages_scrollable_frame = tk.Frame(
            self.messages_canvas,
            bg=self.colors['bg_primary']
        )
        
        self.messages_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.messages_canvas.configure(scrollregion=self.messages_canvas.bbox("all"))
        )
        
        self.messages_canvas.create_window((0, 0), window=self.messages_scrollable_frame, anchor="nw")
        self.messages_canvas.configure(yscrollcommand=self.messages_scrollbar.set)
        
        self.messages_canvas.pack(side="left", fill="both", expand=True)
        self.messages_scrollbar.pack(side="right", fill="y")
        
        # Mouse wheel binding
        self.messages_canvas.bind("<MouseWheel>", self._on_mousewheel)
        
    def setup_input_area(self):
        """Set up the input area at the bottom."""
        input_container = tk.Frame(self.content_frame, bg=self.colors['bg_primary'])
        input_container.pack(fill=tk.X, padx=20, pady=20)
        
        # Input frame
        input_frame = tk.Frame(
            input_container,
            bg=self.colors['bg_tertiary'],
            relief=tk.SOLID,
            bd=1
        )
        input_frame.pack(fill=tk.X)
        
        # Text input
        self.input_text = tk.Text(
            input_frame,
            height=3,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            font=('Arial', 11),
            relief=tk.FLAT,
            wrap=tk.WORD
        )
        self.input_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Send button
        send_btn = tk.Button(
            input_frame,
            text="➤",
            bg=self.colors['accent'],
            fg='white',
            font=('Arial', 14, 'bold'),
            width=3,
            height=3,
            command=self.send_message,
            cursor='hand2'
        )
        send_btn.pack(side=tk.RIGHT, padx=5)
        
        # Bind Enter key
        self.input_text.bind("<Shift-Return>", lambda e: "break")
        self.input_text.bind("<Return>", lambda e: (self.send_message(), "break")[1])
        
    def setup_right_sidebar(self):
        """Set up the right sidebar with tabs."""
        self.right_frame = tk.Frame(
            self.main_container,
            bg=self.colors['bg_secondary'],
            width=300
        )
        self.right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=0, pady=0)
        self.right_frame.pack_propagate(False)
        
        # Tab control
        self.tab_control = ttk.Notebook(self.right_frame)
        self.tab_control.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Topics tab
        self.topics_tab = tk.Frame(self.tab_control, bg=self.colors['bg_secondary'])
        self.tab_control.add(self.topics_tab, text="Topics")
        self.setup_topics_tab()
        
        # Settings tab
        self.settings_tab = tk.Frame(self.tab_control, bg=self.colors['bg_secondary'])
        self.tab_control.add(self.settings_tab, text="Settings")
        self.setup_settings_tab()
        
    def setup_topics_tab(self):
        """Set up the topics tab."""
        # Header
        topics_header = tk.Frame(self.topics_tab, bg=self.colors['bg_secondary'])
        topics_header.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(
            topics_header,
            text="Recent Topics",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        ).pack(side=tk.LEFT)
        
        # Date filter
        self.date_filter_var = tk.StringVar(value="all")
        date_filter = ttk.Combobox(
            topics_header,
            textvariable=self.date_filter_var,
            values=["all", "today", "this_week", "this_month"],
            state="readonly",
            width=12
        )
        date_filter.pack(side=tk.RIGHT)
        
        # Topics list
        self.topic_list_frame = tk.Frame(self.topics_tab, bg=self.colors['bg_secondary'])
        self.topic_list_frame.pack(fill=tk.BOTH, expand=True)
        
        self.update_topic_list()
        
    def setup_settings_tab(self):
        """Set up the settings tab."""
        settings_frame = tk.Frame(self.settings_tab, bg=self.colors['bg_secondary'])
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Model settings
        model_group = tk.LabelFrame(
            settings_frame,
            text="Model Settings",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            font=('Arial', 11, 'bold')
        )
        model_group.pack(fill=tk.X, pady=(0, 15))
        
        # Model selection
        tk.Label(
            model_group,
            text="Model:",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        ).pack(anchor=tk.W, padx=10, pady=(10, 5))
        
        model_combo = ttk.Combobox(
            model_group,
            values=["deepseek-ai/DeepSeek-V3", "gpt-4", "claude-3-sonnet", "llama-3.1-8b"],
            state="readonly"
        )
        model_combo.set("deepseek-ai/DeepSeek-V3")
        model_combo.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # Temperature
        tk.Label(
            model_group,
            text="Temperature:",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        ).pack(anchor=tk.W, padx=10, pady=(10, 5))
        
        temp_scale = ttk.Scale(
            model_group,
            from_=0.0,
            to=2.0,
            value=0.7,
            orient=tk.HORIZONTAL
        )
        temp_scale.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # Appearance settings
        appearance_group = tk.LabelFrame(
            settings_frame,
            text="Appearance",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            font=('Arial', 11, 'bold')
        )
        appearance_group.pack(fill=tk.X, pady=(0, 15))
        
        # Theme selection
        tk.Label(
            appearance_group,
            text="Theme:",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        ).pack(anchor=tk.W, padx=10, pady=(10, 5))
        
        theme_combo = ttk.Combobox(
            appearance_group,
            values=["The Printery", "Dark", "Light"],
            state="readonly"
        )
        theme_combo.set("The Printery")
        theme_combo.pack(fill=tk.X, padx=10, pady=(0, 10))
        
    def on_conversation_select(self, conversation_id: str):
        """Handle conversation selection from the tree."""
        self.current_conversation_id = conversation_id
        # Load conversation messages
        self.load_conversation(conversation_id)
        
    def on_conversation_rename(self, conversation_id: str, new_name: str):
        """Handle conversation rename."""
        self.category_manager.rename_conversation(conversation_id, new_name)
        self.save_data()
        
    def on_conversation_delete(self, conversation_id: str):
        """Handle conversation deletion."""
        if messagebox.askyesno("Delete Conversation", "Are you sure you want to delete this conversation?"):
            self.category_manager.delete_conversation(conversation_id)
            self.save_data()
            if self.current_conversation_id == conversation_id:
                self.current_conversation_id = None
                self.clear_chat()
                
    def create_new_conversation(self):
        """Create a new conversation."""
        title = simpledialog.askstring("New Conversation", "Enter conversation title:")
        if title:
            conversation_id = self.category_manager.create_conversation(title)
            self.save_data()
            self.conversation_tree.refresh()
            self.on_conversation_select(conversation_id)
            
    def load_conversation(self, conversation_id: str):
        """Load a conversation's messages."""
        conversation = self.category_manager.get_conversation(conversation_id)
        if conversation:
            self.current_conversation_id = conversation_id
            self.clear_chat()
            
            # Load messages
            messages = conversation.get('messages', [])
            for msg_data in messages:
                message = Message(
                    content=msg_data['content'],
                    role=msg_data['role'],
                    timestamp=datetime.datetime.fromisoformat(msg_data['timestamp'])
                )
                self.display_message(message)
                
    def clear_chat(self):
        """Clear the chat display."""
        for widget in self.messages_scrollable_frame.winfo_children():
            widget.destroy()
            
    def display_message(self, message: Message):
        """Display a message in the chat."""
        msg_frame = tk.Frame(
            self.messages_scrollable_frame,
            bg=self.colors['bg_primary']
        )
        msg_frame.pack(fill=tk.X, pady=5)
        
        # Message bubble
        bubble = tk.Frame(
            msg_frame,
            bg=self.colors['accent'] if message.role == "assistant" else self.colors['bg_tertiary'],
            padx=15,
            pady=10
        )
        
        if message.role == "user":
            bubble.pack(side=tk.RIGHT, padx=(50, 10))
        else:
            bubble.pack(side=tk.LEFT, padx=(10, 50))
            
        # Message content
        tk.Label(
            bubble,
            text=message.content,
            font=('Arial', 11),
            bg=bubble['bg'],
            fg='white' if message.role == "assistant" else self.colors['text_primary'],
            wraplength=500,
            justify=tk.LEFT
        ).pack()
        
        # Timestamp
        tk.Label(
            bubble,
            text=message.timestamp.strftime("%H:%M"),
            font=('Arial', 8),
            bg=bubble['bg'],
            fg='white' if message.role == "assistant" else self.colors['text_secondary']
        ).pack(anchor=tk.E)
        
    def send_message(self):
        """Send a message."""
        if not self.current_conversation_id:
            messagebox.showwarning("No Conversation", "Please select or create a conversation first.")
            return
            
        content = self.input_text.get("1.0", tk.END).strip()
        if not content:
            return
            
        # Create user message
        user_message = Message(content=content, role="user")
        self.display_message(user_message)
        
        # Add to conversation
        self.category_manager.add_message(self.current_conversation_id, {
            'content': content,
            'role': 'user',
            'timestamp': user_message.timestamp.isoformat()
        })
        
        # Clear input
        self.input_text.delete("1.0", tk.END)
        
        # Simulate assistant response (replace with actual LLM call)
        assistant_message = Message(
            content="This is a simulated response. In a real implementation, this would be generated by your AI assistant.",
            role="assistant"
        )
        self.display_message(assistant_message)
        
        # Add assistant message
        self.category_manager.add_message(self.current_conversation_id, {
            'content': assistant_message.content,
            'role': 'assistant',
            'timestamp': assistant_message.timestamp.isoformat()
        })
        
        self.save_data()
        
    def perform_search(self):
        """Perform search across conversations."""
        query = self.search_entry.get().strip().lower()
        if not query:
            return
            
        results = []
        
        # Search in all conversations
        for conv_id, conversation in self.category_manager.conversations.items():
            # Search in title
            if query in conversation['title'].lower():
                results.append({
                    'type': 'conversation',
                    'id': conv_id,
                    'title': conversation['title'],
                    'match': 'title'
                })
                
            # Search in messages
            for msg in conversation.get('messages', []):
                if query in msg['content'].lower():
                    results.append({
                        'type': 'message',
                        'id': conv_id,
                        'title': conversation['title'],
                        'content': msg['content'][:100] + '...',
                        'match': 'content'
                    })
        
        self.display_search_results(results)
        
    def display_search_results(self, results: List[Dict]):
        """Display search results."""
        # Clear previous content
        for widget in self.content_frame.winfo_children():
            if widget != self.header_frame:
                widget.destroy()
                
        # Results frame
        results_frame = tk.Frame(self.content_frame, bg=self.colors['bg_primary'])
        results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Results header
        header = tk.Frame(results_frame, bg=self.colors['bg_primary'])
        header.pack(fill=tk.X, pady=(0, 20))
        
        tk.Label(
            header,
            text=f"Search Results ({len(results)} found)",
            font=('Arial', 16, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        ).pack(side=tk.LEFT)
        
        # Back button
        back_btn = tk.Button(
            header,
            text="← Back to Chat",
            bg=self.colors['accent'],
            fg='white',
            font=('Arial', 10, 'bold'),
            command=self.setup_chat_content
        )
        back_btn.pack(side=tk.RIGHT)
        
        # Results list
        results_container = tk.Frame(results_frame, bg=self.colors['bg_primary'])
        results_container.pack(fill=tk.BOTH, expand=True)
        
        for result in results:
            result_frame = tk.Frame(
                results_container,
                bg=self.colors['bg_secondary'],
                relief=tk.RAISED,
                bd=1
            )
            result_frame.pack(fill=tk.X, pady=5, padx=5)
            result_frame.bind("<Button-1>", lambda e, cid=result['id']: self.select_conversation_from_search(cid))
            
            # Title
            title_label = tk.Label(
                result_frame,
                text=result['title'],
                font=('Arial', 12, 'bold'),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']
            )
            title_label.pack(anchor=tk.W, padx=10, pady=(10, 5))
            title_label.bind("<Button-1>", lambda e, cid=result['id']: self.select_conversation_from_search(cid))
            
            # Content preview
            if result['type'] == 'message':
                content_label = tk.Label(
                    result_frame,
                    text=result['content'],
                    font=('Arial', 10),
                    bg=self.colors['bg_secondary'],
                    fg=self.colors['text_secondary'],
                    wraplength=600,
                    justify=tk.LEFT
                )
                content_label.pack(anchor=tk.W, padx=10, pady=(0, 10))
                content_label.bind("<Button-1>", lambda e, cid=result['id']: self.select_conversation_from_search(cid))
                
    def select_conversation_from_search(self, conversation_id: str):
        """Select a conversation from search results."""
        self.setup_chat_content()
        self.on_conversation_select(conversation_id)
        self.conversation_tree.select_conversation(conversation_id)
        
    def update_topic_list(self):
        """Update the topic list in the right sidebar."""
        # Clear previous topic list
        for widget in self.topic_list_frame.winfo_children():
            widget.destroy()
            
        # Get recent conversations
        recent_conversations = self.category_manager.get_recent_conversations(limit=10)
        
        for conv in recent_conversations:
            topic_frame = tk.Frame(self.topic_list_frame, bg=self.colors['bg_secondary'])
            topic_frame.pack(fill=tk.X, pady=2)
            topic_frame.bind("<Button-1>", lambda e, cid=conv['id']: self.on_conversation_select(cid))
            
            tk.Label(
                topic_frame,
                text=conv['title'],
                font=('Arial', 10),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']
            ).pack(side=tk.LEFT, padx=10)
            
    def load_data(self):
        """Load conversations and categories from file."""
        self.category_manager.load_from_file()
        
    def save_data(self):
        """Save conversations and categories to file."""
        self.category_manager.save_to_file()
        
    def on_closing(self):
        """Handle window closing."""
        self.save_data()
        self.destroy()
        
    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        self.messages_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
    def rename_topic_prompt(self, topic_index):
        """Show rename prompt for a topic."""
        # This method is now handled by the conversation tree
        pass
