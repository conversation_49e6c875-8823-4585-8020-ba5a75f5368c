"""
Provider status window for monitoring LLM provider health and capabilities.
"""
import tkinter as tk
from tkinter import ttk
import asyncio
import logging
from typing import Dict, List, Optional
from pathlib import Path

from ..llm import ProviderManager, ProviderInfo

logger = logging.getLogger(__name__)

class ProviderStatusWindow(tk.Toplevel):
    """Window for displaying LLM provider status and capabilities."""
    
    def __init__(self, parent, config):
        super().__init__(parent)
        self.parent = parent
        self.config = config
        
        # Window configuration
        self.title("LLM Provider Status")
        self.geometry("800x600")
        self.minsize(600, 400)
        
        # Initialize provider manager
        self.provider_manager = ProviderManager(config)
        self.provider_status: Dict[str, bool] = {}
        self.provider_models: Dict[str, List[str]] = {}
        
        # Set up UI
        self.setup_ui()
        
        # Start health checks
        if hasattr(parent, 'schedule_task'):
            parent.schedule_task(self.check_all_providers())
    
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(
            main_frame,
            text="🔌 LLM Provider Status",
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # Create notebook for different views
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Provider status tab
        self.setup_status_tab()
        
        # Provider info tab
        self.setup_info_tab()
        
        # Models tab
        self.setup_models_tab()
        
        # Refresh button
        refresh_btn = ttk.Button(
            main_frame,
            text="🔄 Refresh All",
            command=self.refresh_all
        )
        refresh_btn.pack(pady=(10, 0))
    
    def setup_status_tab(self):
        """Set up the provider status tab."""
        status_frame = ttk.Frame(self.notebook)
        self.notebook.add(status_frame, text="Status")
        
        # Create treeview for status
        columns = ('Provider', 'Status', 'Type', 'Models')
        self.status_tree = ttk.Treeview(status_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.status_tree.heading('Provider', text='Provider')
        self.status_tree.heading('Status', text='Status')
        self.status_tree.heading('Type', text='Type')
        self.status_tree.heading('Models', text='Available Models')
        
        self.status_tree.column('Provider', width=150)
        self.status_tree.column('Status', width=100)
        self.status_tree.column('Type', width=100)
        self.status_tree.column('Models', width=400)
        
        # Scrollbar for treeview
        status_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_tree.yview)
        self.status_tree.configure(yscrollcommand=status_scrollbar.set)
        
        # Pack treeview and scrollbar
        self.status_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        status_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_info_tab(self):
        """Set up the provider information tab."""
        info_frame = ttk.Frame(self.notebook)
        self.notebook.add(info_frame, text="Provider Info")
        
        # Create text widget for detailed info
        self.info_text = tk.Text(
            info_frame,
            wrap=tk.WORD,
            font=('Consolas', 10),
            state=tk.DISABLED
        )
        
        # Scrollbar for text widget
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        # Pack text widget and scrollbar
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Populate provider info
        self.populate_provider_info()
    
    def setup_models_tab(self):
        """Set up the models tab."""
        models_frame = ttk.Frame(self.notebook)
        self.notebook.add(models_frame, text="Models")
        
        # Create treeview for models
        model_columns = ('Provider', 'Model', 'Capabilities')
        self.models_tree = ttk.Treeview(models_frame, columns=model_columns, show='headings', height=20)
        
        # Configure columns
        self.models_tree.heading('Provider', text='Provider')
        self.models_tree.heading('Model', text='Model')
        self.models_tree.heading('Capabilities', text='Capabilities')
        
        self.models_tree.column('Provider', width=150)
        self.models_tree.column('Model', width=300)
        self.models_tree.column('Capabilities', width=300)
        
        # Scrollbar for models treeview
        models_scrollbar = ttk.Scrollbar(models_frame, orient=tk.VERTICAL, command=self.models_tree.yview)
        self.models_tree.configure(yscrollcommand=models_scrollbar.set)
        
        # Pack treeview and scrollbar
        self.models_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        models_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def populate_provider_info(self):
        """Populate the provider information tab."""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        
        info_text = "🔌 LLM Provider Information\n"
        info_text += "=" * 50 + "\n\n"
        
        providers = self.provider_manager.get_all_providers()
        
        for provider in providers:
            info_text += f"📡 {provider.display_name} ({provider.name})\n"
            info_text += f"   Description: {provider.description}\n"
            info_text += f"   Type: {'Local' if provider.is_local else 'Cloud'}\n"
            info_text += f"   API Key Required: {'Yes' if provider.requires_api_key else 'No'}\n"
            info_text += f"   Streaming: {'Yes' if provider.supports_streaming else 'No'}\n"
            info_text += f"   Vision: {'Yes' if provider.supports_vision else 'No'}\n"
            info_text += f"   Function Calling: {'Yes' if provider.supports_function_calling else 'No'}\n"
            info_text += f"   Default Models: {', '.join(provider.default_models[:3])}{'...' if len(provider.default_models) > 3 else ''}\n"
            info_text += "\n"
        
        info_text += "\n🌟 Recommended Providers:\n"
        info_text += "=" * 30 + "\n"
        info_text += "• OpenAI: Most capable, excellent for general use\n"
        info_text += "• Anthropic: Great for analysis and reasoning\n"
        info_text += "• Groq: Ultra-fast inference, good for real-time chat\n"
        info_text += "• Ollama: Best for privacy and local deployment\n"
        info_text += "• Perplexity: Excellent for research with web search\n"
        info_text += "• vLLM: High-performance local inference\n"
        
        self.info_text.insert(1.0, info_text)
        self.info_text.config(state=tk.DISABLED)
    
    async def check_all_providers(self):
        """Check the health status of all providers."""
        providers = self.provider_manager.get_all_providers()
        
        for provider in providers:
            try:
                # Update status to "Checking..."
                self.update_provider_status(provider.name, "Checking...", provider)
                
                # Perform health check
                is_healthy = await self.provider_manager.health_check_provider(provider.name)
                self.provider_status[provider.name] = is_healthy
                
                # Get available models
                if is_healthy:
                    models = await self.provider_manager.get_available_models(provider.name)
                    self.provider_models[provider.name] = models
                else:
                    self.provider_models[provider.name] = []
                
                # Update UI
                status_text = "✅ Online" if is_healthy else "❌ Offline"
                self.update_provider_status(provider.name, status_text, provider)
                
            except Exception as e:
                logger.error(f"Error checking provider {provider.name}: {e}")
                self.provider_status[provider.name] = False
                self.provider_models[provider.name] = []
                self.update_provider_status(provider.name, "❌ Error", provider)
        
        # Update models tab
        self.update_models_tab()
    
    def update_provider_status(self, provider_name: str, status: str, provider_info: ProviderInfo):
        """Update the status display for a provider."""
        # Clear existing items for this provider
        for item in self.status_tree.get_children():
            if self.status_tree.item(item)['values'][0] == provider_info.display_name:
                self.status_tree.delete(item)
        
        # Get model count
        models = self.provider_models.get(provider_name, [])
        model_count = f"{len(models)} models" if models else "No models"
        
        # Add/update the provider row
        provider_type = "Local" if provider_info.is_local else "Cloud"
        
        self.status_tree.insert('', 'end', values=(
            provider_info.display_name,
            status,
            provider_type,
            model_count
        ))
    
    def update_models_tab(self):
        """Update the models tab with current model information."""
        # Clear existing items
        for item in self.models_tree.get_children():
            self.models_tree.delete(item)
        
        # Add models for each provider
        providers = self.provider_manager.get_all_providers()
        
        for provider in providers:
            models = self.provider_models.get(provider.name, [])
            
            if not models:
                # Show provider even if no models available
                capabilities = self.get_provider_capabilities(provider)
                self.models_tree.insert('', 'end', values=(
                    provider.display_name,
                    "No models available",
                    capabilities
                ))
            else:
                # Add each model
                for model in models:
                    capabilities = self.get_provider_capabilities(provider)
                    self.models_tree.insert('', 'end', values=(
                        provider.display_name,
                        model,
                        capabilities
                    ))
    
    def get_provider_capabilities(self, provider: ProviderInfo) -> str:
        """Get a string describing provider capabilities."""
        capabilities = []
        
        if provider.supports_streaming:
            capabilities.append("Streaming")
        if provider.supports_vision:
            capabilities.append("Vision")
        if provider.supports_function_calling:
            capabilities.append("Functions")
        if provider.is_local:
            capabilities.append("Local")
        else:
            capabilities.append("Cloud")
        
        return ", ".join(capabilities)
    
    def refresh_all(self):
        """Refresh all provider information."""
        if hasattr(self.parent, 'schedule_task'):
            self.parent.schedule_task(self.check_all_providers())
        else:
            # Fallback for testing
            self.after(100, lambda: asyncio.create_task(self.check_all_providers()))
