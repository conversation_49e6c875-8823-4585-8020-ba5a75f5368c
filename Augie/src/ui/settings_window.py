"""
Settings window with tabbed interface for The Printery AI Assistant.
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional

from .themes import ThemeManager
from ..mcp.server_manager import MCPServerManager
from ..mcp.basic_servers import validate_mcp_server_requirements, get_mcp_setup_instructions
from ..assistants import AssistantManager

logger = logging.getLogger(__name__)

class SettingsWindow(tk.Toplevel):
    """Settings window with tabbed interface."""
    
    def __init__(self, parent, config=None):
        super().__init__(parent)
        self.parent = parent
        self.config = config or {}
        
        # Window configuration
        self.title("The Printery AI Assistant - Settings")
        self.geometry("800x600")
        self.minsize(700, 500)
        
        # Initialize managers
        self.theme_manager = ThemeManager()
        self.mcp_manager = MCPServerManager()
        self.assistant_manager = AssistantManager()
        
        # Apply current theme
        self.apply_theme()
        
        # Set up the tabbed interface
        self.setup_ui()
        
        # Make window modal
        self.transient(parent)
        self.grab_set()
        
        # Center the window
        self.center_window()
    
    def apply_theme(self):
        """Apply the current theme to the window."""
        current_theme = self.theme_manager.get_current_theme()
        self.colors = current_theme.colors
        self.configure(bg=self.colors['bg_primary'])
    
    def center_window(self):
        """Center the window on the parent."""
        self.update_idletasks()
        x = (self.parent.winfo_x() + (self.parent.winfo_width() // 2) - 
             (self.winfo_width() // 2))
        y = (self.parent.winfo_y() + (self.parent.winfo_height() // 2) - 
             (self.winfo_height() // 2))
        self.geometry(f"+{x}+{y}")
    
    def setup_ui(self):
        """Set up the tabbed user interface."""
        # Main container
        main_frame = tk.Frame(self, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Header
        header_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = tk.Label(
            header_frame,
            text="🍒 The Printery AI Assistant Settings",
            font=('Arial', 16, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        )
        title_label.pack()
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Create tabs
        self.setup_general_tab()
        self.setup_themes_tab()
        self.setup_assistants_tab()
        self.setup_mcp_tab()
        self.setup_memory_tab()
        self.setup_advanced_tab()
        
        # Bottom buttons
        self.setup_bottom_buttons(main_frame)
    
    def setup_general_tab(self):
        """Set up the General settings tab."""
        general_frame = ttk.Frame(self.notebook)
        self.notebook.add(general_frame, text="General")
        
        # Scrollable frame
        canvas = tk.Canvas(general_frame, bg=self.colors['bg_primary'])
        scrollbar = ttk.Scrollbar(general_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_primary'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # General settings content
        self.setup_general_content(scrollable_frame)
    
    def setup_general_content(self, parent):
        """Set up general settings content."""
        # Application Settings
        app_frame = tk.LabelFrame(
            parent,
            text="Application Settings",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        app_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Auto-save conversations
        self.auto_save_var = tk.BooleanVar(value=self.config.get('auto_save', True))
        auto_save_cb = tk.Checkbutton(
            app_frame,
            text="Auto-save conversations",
            variable=self.auto_save_var,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            selectcolor=self.colors['bg_secondary']
        )
        auto_save_cb.pack(anchor=tk.W, pady=2)
        
        # Auto-start MCP servers
        self.auto_start_mcp_var = tk.BooleanVar(value=self.config.get('auto_start_mcp', True))
        auto_start_cb = tk.Checkbutton(
            app_frame,
            text="Auto-start MCP servers on launch",
            variable=self.auto_start_mcp_var,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            selectcolor=self.colors['bg_secondary']
        )
        auto_start_cb.pack(anchor=tk.W, pady=2)
        
        # Show keyboard shortcuts
        shortcuts_btn = tk.Button(
            app_frame,
            text="Show Keyboard Shortcuts",
            command=self.show_keyboard_shortcuts,
            bg=self.colors['accent'],
            fg='white',
            cursor='hand2'
        )
        shortcuts_btn.pack(anchor=tk.W, pady=5)
        
        # Data Management
        data_frame = tk.LabelFrame(
            parent,
            text="Data Management",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        data_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Data directory
        data_dir_frame = tk.Frame(data_frame, bg=self.colors['bg_primary'])
        data_dir_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(
            data_dir_frame,
            text="Data Directory:",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        ).pack(side=tk.LEFT)
        
        self.data_dir_var = tk.StringVar(value=self.config.get('data_dir', 'data'))
        data_dir_entry = tk.Entry(
            data_dir_frame,
            textvariable=self.data_dir_var,
            width=40,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )
        data_dir_entry.pack(side=tk.LEFT, padx=(10, 5))
        
        browse_btn = tk.Button(
            data_dir_frame,
            text="Browse",
            command=self.browse_data_directory,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            cursor='hand2'
        )
        browse_btn.pack(side=tk.LEFT)
        
        # Export/Import buttons
        export_import_frame = tk.Frame(data_frame, bg=self.colors['bg_primary'])
        export_import_frame.pack(fill=tk.X, pady=10)
        
        export_btn = tk.Button(
            export_import_frame,
            text="Export Settings",
            command=self.export_settings,
            bg=self.colors['accent'],
            fg='white',
            cursor='hand2'
        )
        export_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        import_btn = tk.Button(
            export_import_frame,
            text="Import Settings",
            command=self.import_settings,
            bg=self.colors['accent'],
            fg='white',
            cursor='hand2'
        )
        import_btn.pack(side=tk.LEFT)
    
    def setup_themes_tab(self):
        """Set up the Themes tab."""
        themes_frame = ttk.Frame(self.notebook)
        self.notebook.add(themes_frame, text="Themes")
        
        # Main content frame
        content_frame = tk.Frame(themes_frame, bg=self.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Current theme section
        current_frame = tk.LabelFrame(
            content_frame,
            text="Current Theme",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        current_frame.pack(fill=tk.X, pady=(0, 10))
        
        current_theme = self.theme_manager.get_current_theme()
        
        # Theme selector
        theme_select_frame = tk.Frame(current_frame, bg=self.colors['bg_primary'])
        theme_select_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(
            theme_select_frame,
            text="Select Theme:",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            font=('Arial', 10, 'bold')
        ).pack(side=tk.LEFT)
        
        self.theme_var = tk.StringVar(value=current_theme.display_name)
        theme_combo = ttk.Combobox(
            theme_select_frame,
            textvariable=self.theme_var,
            values=self.theme_manager.get_theme_names(),
            state="readonly",
            width=25
        )
        theme_combo.pack(side=tk.LEFT, padx=(10, 0))
        theme_combo.bind('<<ComboboxSelected>>', self.on_theme_changed)
        
        # Color preview
        preview_frame = tk.LabelFrame(
            content_frame,
            text="Color Preview",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        preview_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.setup_color_preview(preview_frame)
        
        # Theme description
        desc_frame = tk.LabelFrame(
            content_frame,
            text="Theme Description",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        desc_frame.pack(fill=tk.BOTH, expand=True)
        
        self.theme_desc_label = tk.Label(
            desc_frame,
            text=current_theme.description,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary'],
            wraplength=400,
            justify=tk.LEFT
        )
        self.theme_desc_label.pack(anchor=tk.W)
    
    def setup_color_preview(self, parent):
        """Set up color preview section."""
        colors_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        colors_frame.pack(fill=tk.X, pady=5)
        
        current_theme = self.theme_manager.get_current_theme()
        
        # The Printery colors
        if 'printery_pink' in current_theme.colors:
            pink_sample = tk.Label(
                colors_frame,
                text="  The Printery Pink  ",
                bg=current_theme.colors['printery_pink'],
                fg='black',
                font=('Arial', 10, 'bold')
            )
            pink_sample.pack(side=tk.LEFT, padx=(0, 10))
        
        if 'printery_teal' in current_theme.colors:
            teal_sample = tk.Label(
                colors_frame,
                text="  The Printery Teal  ",
                bg=current_theme.colors['printery_teal'],
                fg='white',
                font=('Arial', 10, 'bold')
            )
            teal_sample.pack(side=tk.LEFT, padx=(0, 10))
        
        # User message color
        user_sample = tk.Label(
            colors_frame,
            text="  User Message  ",
            bg=current_theme.colors['user_msg'],
            fg='white' if current_theme.is_dark else 'black',
            font=('Arial', 10)
        )
        user_sample.pack(side=tk.LEFT, padx=(0, 10))
        
        # Assistant message color
        assistant_sample = tk.Label(
            colors_frame,
            text="  Assistant Message  ",
            bg=current_theme.colors['assistant_msg'],
            fg='black' if not current_theme.is_dark else 'white',
            font=('Arial', 10)
        )
        assistant_sample.pack(side=tk.LEFT)
    
    def setup_assistants_tab(self):
        """Set up the AI Assistants tab."""
        assistants_frame = ttk.Frame(self.notebook)
        self.notebook.add(assistants_frame, text="AI Assistants")
        
        # Main content
        content_frame = tk.Frame(assistants_frame, bg=self.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Assistants list
        list_frame = tk.LabelFrame(
            content_frame,
            text="Available Assistants",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Create treeview for assistants
        columns = ('Name', 'Model', 'Temperature', 'Description')
        self.assistants_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        # Configure columns
        self.assistants_tree.heading('Name', text='Name')
        self.assistants_tree.heading('Model', text='Preferred Model')
        self.assistants_tree.heading('Temperature', text='Temperature')
        self.assistants_tree.heading('Description', text='Description')
        
        self.assistants_tree.column('Name', width=120)
        self.assistants_tree.column('Model', width=150)
        self.assistants_tree.column('Temperature', width=80)
        self.assistants_tree.column('Description', width=300)
        
        # Scrollbar for treeview
        tree_scroll = ttk.Scrollbar(list_frame, orient="vertical", command=self.assistants_tree.yview)
        self.assistants_tree.configure(yscrollcommand=tree_scroll.set)
        
        self.assistants_tree.pack(side="left", fill="both", expand=True)
        tree_scroll.pack(side="right", fill="y")
        
        # Populate assistants
        self.refresh_assistants_list()
        
        # Buttons frame
        buttons_frame = tk.Frame(content_frame, bg=self.colors['bg_primary'])
        buttons_frame.pack(fill=tk.X)
        
        add_btn = tk.Button(
            buttons_frame,
            text="Add Assistant",
            command=self.add_assistant,
            bg=self.colors['accent'],
            fg='white',
            cursor='hand2'
        )
        add_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        edit_btn = tk.Button(
            buttons_frame,
            text="Edit Selected",
            command=self.edit_assistant,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            cursor='hand2'
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        delete_btn = tk.Button(
            buttons_frame,
            text="Delete Selected",
            command=self.delete_assistant,
            bg=self.colors['danger'],
            fg='white',
            cursor='hand2'
        )
        delete_btn.pack(side=tk.LEFT)
    
    def setup_mcp_tab(self):
        """Set up the MCP (Model Context Protocol) tab."""
        mcp_frame = ttk.Frame(self.notebook)
        self.notebook.add(mcp_frame, text="MCP Servers")
        
        # Main content
        content_frame = tk.Frame(mcp_frame, bg=self.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Requirements check
        req_frame = tk.LabelFrame(
            content_frame,
            text="System Requirements",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        req_frame.pack(fill=tk.X, pady=(0, 10))
        
        requirements = validate_mcp_server_requirements()
        
        for req, status in requirements.items():
            status_text = "✅" if status else "❌"
            color = 'green' if status else 'red'
            
            req_label = tk.Label(
                req_frame,
                text=f"{status_text} {req}: {'Available' if status else 'Missing'}",
                bg=self.colors['bg_primary'],
                fg=color,
                font=('Arial', 10)
            )
            req_label.pack(anchor=tk.W)
        
        # Setup instructions button
        if not all(requirements.values()):
            setup_btn = tk.Button(
                req_frame,
                text="Show Setup Instructions",
                command=self.show_mcp_instructions,
                bg=self.colors['warning'],
                fg='black',
                cursor='hand2'
            )
            setup_btn.pack(anchor=tk.W, pady=(5, 0))
        
        # MCP Servers list
        servers_frame = tk.LabelFrame(
            content_frame,
            text="MCP Servers",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        servers_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Create treeview for servers
        server_columns = ('Name', 'Status', 'Enabled', 'Auto-Start', 'Tools', 'Description')
        self.servers_tree = ttk.Treeview(servers_frame, columns=server_columns, show='headings', height=8)
        
        # Configure columns
        for col in server_columns:
            self.servers_tree.heading(col, text=col)
        
        self.servers_tree.column('Name', width=120)
        self.servers_tree.column('Status', width=80)
        self.servers_tree.column('Enabled', width=60)
        self.servers_tree.column('Auto-Start', width=80)
        self.servers_tree.column('Tools', width=50)
        self.servers_tree.column('Description', width=250)
        
        # Scrollbar for servers treeview
        servers_scroll = ttk.Scrollbar(servers_frame, orient="vertical", command=self.servers_tree.yview)
        self.servers_tree.configure(yscrollcommand=servers_scroll.set)
        
        self.servers_tree.pack(side="left", fill="both", expand=True)
        servers_scroll.pack(side="right", fill="y")
        
        # Populate servers
        self.refresh_servers_list()
        
        # Server control buttons
        server_buttons_frame = tk.Frame(content_frame, bg=self.colors['bg_primary'])
        server_buttons_frame.pack(fill=tk.X)
        
        start_btn = tk.Button(
            server_buttons_frame,
            text="Start Server",
            command=self.start_selected_server,
            bg=self.colors['success'],
            fg='white',
            cursor='hand2'
        )
        start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        stop_btn = tk.Button(
            server_buttons_frame,
            text="Stop Server",
            command=self.stop_selected_server,
            bg=self.colors['danger'],
            fg='white',
            cursor='hand2'
        )
        stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        toggle_btn = tk.Button(
            server_buttons_frame,
            text="Toggle Enabled",
            command=self.toggle_server_enabled,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            cursor='hand2'
        )
        toggle_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        refresh_btn = tk.Button(
            server_buttons_frame,
            text="Refresh",
            command=self.refresh_servers_list,
            bg=self.colors['accent'],
            fg='white',
            cursor='hand2'
        )
        refresh_btn.pack(side=tk.LEFT)
    
    def setup_memory_tab(self):
        """Set up the Memory & Context tab."""
        memory_frame = ttk.Frame(self.notebook)
        self.notebook.add(memory_frame, text="Memory & Context")
        
        # Main content
        content_frame = tk.Frame(memory_frame, bg=self.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Context settings
        context_frame = tk.LabelFrame(
            content_frame,
            text="Context Management",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        context_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Max context length
        context_length_frame = tk.Frame(context_frame, bg=self.colors['bg_primary'])
        context_length_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(
            context_length_frame,
            text="Max Context Length:",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        ).pack(side=tk.LEFT)
        
        self.context_length_var = tk.StringVar(value=self.config.get('max_context_length', '4000'))
        context_entry = tk.Entry(
            context_length_frame,
            textvariable=self.context_length_var,
            width=10,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )
        context_entry.pack(side=tk.LEFT, padx=(10, 5))
        
        tk.Label(
            context_length_frame,
            text="tokens",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary']
        ).pack(side=tk.LEFT)
        
        # Memory settings
        memory_settings_frame = tk.LabelFrame(
            content_frame,
            text="Memory Settings",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        memory_settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Remember conversation context
        self.remember_context_var = tk.BooleanVar(value=self.config.get('remember_context', True))
        remember_cb = tk.Checkbutton(
            memory_settings_frame,
            text="Remember conversation context between sessions",
            variable=self.remember_context_var,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            selectcolor=self.colors['bg_secondary']
        )
        remember_cb.pack(anchor=tk.W, pady=2)
        
        # Auto-summarize long conversations
        self.auto_summarize_var = tk.BooleanVar(value=self.config.get('auto_summarize', False))
        summarize_cb = tk.Checkbutton(
            memory_settings_frame,
            text="Auto-summarize conversations when they get too long",
            variable=self.auto_summarize_var,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            selectcolor=self.colors['bg_secondary']
        )
        summarize_cb.pack(anchor=tk.W, pady=2)
        
        # Conversation management
        conv_mgmt_frame = tk.LabelFrame(
            content_frame,
            text="Conversation Management",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        conv_mgmt_frame.pack(fill=tk.BOTH, expand=True)
        
        # Conversation statistics
        stats_text = self.get_conversation_stats()
        stats_label = tk.Label(
            conv_mgmt_frame,
            text=stats_text,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            justify=tk.LEFT
        )
        stats_label.pack(anchor=tk.W, pady=5)
        
        # Management buttons
        mgmt_buttons_frame = tk.Frame(conv_mgmt_frame, bg=self.colors['bg_primary'])
        mgmt_buttons_frame.pack(fill=tk.X, pady=10)
        
        clear_btn = tk.Button(
            mgmt_buttons_frame,
            text="Clear All Conversations",
            command=self.clear_conversations,
            bg=self.colors['danger'],
            fg='white',
            cursor='hand2'
        )
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        export_conv_btn = tk.Button(
            mgmt_buttons_frame,
            text="Export Conversations",
            command=self.export_conversations,
            bg=self.colors['accent'],
            fg='white',
            cursor='hand2'
        )
        export_conv_btn.pack(side=tk.LEFT)
    
    def setup_advanced_tab(self):
        """Set up the Advanced settings tab."""
        advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(advanced_frame, text="Advanced")
        
        # Main content
        content_frame = tk.Frame(advanced_frame, bg=self.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Logging settings
        logging_frame = tk.LabelFrame(
            content_frame,
            text="Logging & Debug",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        logging_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Log level
        log_level_frame = tk.Frame(logging_frame, bg=self.colors['bg_primary'])
        log_level_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(
            log_level_frame,
            text="Log Level:",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        ).pack(side=tk.LEFT)
        
        self.log_level_var = tk.StringVar(value=self.config.get('log_level', 'INFO'))
        log_level_combo = ttk.Combobox(
            log_level_frame,
            textvariable=self.log_level_var,
            values=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            state="readonly",
            width=15
        )
        log_level_combo.pack(side=tk.LEFT, padx=(10, 0))
        
        # Enable debug mode
        self.debug_mode_var = tk.BooleanVar(value=self.config.get('debug_mode', False))
        debug_cb = tk.Checkbutton(
            logging_frame,
            text="Enable debug mode (verbose logging)",
            variable=self.debug_mode_var,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            selectcolor=self.colors['bg_secondary']
        )
        debug_cb.pack(anchor=tk.W, pady=2)
        
        # Performance settings
        perf_frame = tk.LabelFrame(
            content_frame,
            text="Performance",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        perf_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Request timeout
        timeout_frame = tk.Frame(perf_frame, bg=self.colors['bg_primary'])
        timeout_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(
            timeout_frame,
            text="Request Timeout:",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        ).pack(side=tk.LEFT)
        
        self.timeout_var = tk.StringVar(value=self.config.get('request_timeout', '30'))
        timeout_entry = tk.Entry(
            timeout_frame,
            textvariable=self.timeout_var,
            width=10,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )
        timeout_entry.pack(side=tk.LEFT, padx=(10, 5))
        
        tk.Label(
            timeout_frame,
            text="seconds",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary']
        ).pack(side=tk.LEFT)
        
        # Reset to defaults
        reset_frame = tk.LabelFrame(
            content_frame,
            text="Reset Settings",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            padx=10,
            pady=10
        )
        reset_frame.pack(fill=tk.X)
        
        reset_btn = tk.Button(
            reset_frame,
            text="Reset All Settings to Defaults",
            command=self.reset_to_defaults,
            bg=self.colors['danger'],
            fg='white',
            cursor='hand2'
        )
        reset_btn.pack(anchor=tk.W, pady=5)
        
        tk.Label(
            reset_frame,
            text="Warning: This will reset all settings to their default values.",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 9)
        ).pack(anchor=tk.W)
    
    def setup_bottom_buttons(self, parent):
        """Set up bottom action buttons."""
        buttons_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Cancel button
        cancel_btn = tk.Button(
            buttons_frame,
            text="Cancel",
            command=self.cancel_settings,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            cursor='hand2',
            width=12
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Apply button
        apply_btn = tk.Button(
            buttons_frame,
            text="Apply",
            command=self.apply_settings,
            bg=self.colors['accent'],
            fg='white',
            cursor='hand2',
            width=12
        )
        apply_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # OK button
        ok_btn = tk.Button(
            buttons_frame,
            text="OK",
            command=self.ok_settings,
            bg=self.colors['success'],
            fg='white',
            cursor='hand2',
            width=12
        )
        ok_btn.pack(side=tk.RIGHT)
    
    # Event handlers and utility methods
    def on_theme_changed(self, event):
        """Handle theme selection change."""
        selected_theme_name = self.theme_var.get()
        
        # Find the theme by display name
        themes = self.theme_manager.get_all_themes()
        for theme in themes:
            if theme.display_name == selected_theme_name:
                self.theme_manager.set_current_theme(theme.name)
                break
        
        # Update the preview and description
        self.apply_theme()
        self.refresh_color_preview()
        
        current_theme = self.theme_manager.get_current_theme()
        self.theme_desc_label.config(text=current_theme.description)
        
        logger.info(f"Theme changed to: {selected_theme_name}")
    
    def refresh_color_preview(self):
        """Refresh the color preview section."""
        # This would be called after theme changes to update colors
        pass
    
    def show_keyboard_shortcuts(self):
        """Show keyboard shortcuts dialog."""
        shortcuts_text = """Keyboard Shortcuts:

Ctrl+N          New conversation
Ctrl+Enter      Send message (alternative to Enter)
Enter           Send message (when not holding Shift)
Shift+Enter     New line in message
Escape          Focus message input
Ctrl+/          Show this help

Navigation:
- Click conversations in sidebar to switch
- Use mouse wheel to scroll messages
- Use Tab to navigate between UI elements"""
        
        messagebox.showinfo("Keyboard Shortcuts", shortcuts_text)
    
    def browse_data_directory(self):
        """Browse for data directory."""
        directory = filedialog.askdirectory(
            title="Select Data Directory",
            initialdir=self.data_dir_var.get()
        )
        if directory:
            self.data_dir_var.set(directory)
    
    def export_settings(self):
        """Export settings to file."""
        file_path = filedialog.asksaveasfilename(
            title="Export Settings",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            try:
                settings = self.collect_all_settings()
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("Success", f"Settings exported to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export settings:\n{str(e)}")
    
    def import_settings(self):
        """Import settings from file."""
        file_path = filedialog.askopenfilename(
            title="Import Settings",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                self.apply_imported_settings(settings)
                messagebox.showinfo("Success", f"Settings imported from {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to import settings:\n{str(e)}")
    
    def refresh_assistants_list(self):
        """Refresh the assistants list."""
        # Clear existing items
        for item in self.assistants_tree.get_children():
            self.assistants_tree.delete(item)
        
        # Add assistants
        assistants = self.assistant_manager.get_all_assistants()
        for assistant in assistants:
            self.assistants_tree.insert('', 'end', values=(
                assistant.name,
                assistant.preferred_model or 'Default',
                assistant.temperature,
                assistant.description[:50] + ('...' if len(assistant.description) > 50 else '')
            ))
    
    def add_assistant(self):
        """Add a new assistant."""
        messagebox.showinfo("Add Assistant", "Assistant creation dialog would open here")
    
    def edit_assistant(self):
        """Edit selected assistant."""
        selection = self.assistants_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select an assistant to edit")
            return
        messagebox.showinfo("Edit Assistant", "Assistant editing dialog would open here")
    
    def delete_assistant(self):
        """Delete selected assistant."""
        selection = self.assistants_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select an assistant to delete")
            return
        
        if messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this assistant?"):
            messagebox.showinfo("Delete Assistant", "Assistant would be deleted here")
    
    def show_mcp_instructions(self):
        """Show MCP setup instructions."""
        instructions = get_mcp_setup_instructions()
        
        # Create instructions window
        inst_window = tk.Toplevel(self)
        inst_window.title("MCP Setup Instructions")
        inst_window.geometry("600x500")
        
        text_widget = tk.Text(
            inst_window,
            wrap=tk.WORD,
            font=('Arial', 10),
            padx=10,
            pady=10
        )
        text_widget.pack(fill=tk.BOTH, expand=True)
        
        text_widget.insert('1.0', instructions)
        text_widget.config(state='disabled')
        
        # Close button
        close_btn = tk.Button(
            inst_window,
            text="Close",
            command=inst_window.destroy,
            bg=self.colors['accent'],
            fg='white',
            cursor='hand2'
        )
        close_btn.pack(pady=10)
    
    def refresh_servers_list(self):
        """Refresh the MCP servers list."""
        # Clear existing items
        for item in self.servers_tree.get_children():
            self.servers_tree.delete(item)
        
        # Get server status
        server_status = self.mcp_manager.get_server_status()
        
        # Add servers
        for server_name, status in server_status.items():
            if server_name == "local_tools":
                continue  # Skip local tools in this list
            
            server = self.mcp_manager.servers.get(server_name)
            if server:
                self.servers_tree.insert('', 'end', values=(
                    status['name'],
                    'Connected' if status['connected'] else 'Disconnected',
                    'Yes' if status['enabled'] else 'No',
                    'Yes' if status.get('auto_start', False) else 'No',
                    status['tools_count'],
                    server.description[:40] + ('...' if len(server.description) > 40 else '')
                ))
    
    def start_selected_server(self):
        """Start the selected MCP server."""
        selection = self.servers_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a server to start")
            return
        
        # Get server name from selection
        item = self.servers_tree.item(selection[0])
        server_name = None
        for name, server in self.mcp_manager.servers.items():
            if server.display_name == item['values'][0]:
                server_name = name
                break
        
        if server_name:
            # Start server asynchronously
            messagebox.showinfo("Start Server", f"Starting server {server_name}...")
            # In real implementation, this would be async
    
    def stop_selected_server(self):
        """Stop the selected MCP server."""
        selection = self.servers_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a server to stop")
            return
        messagebox.showinfo("Stop Server", "Server would be stopped here")
    
    def toggle_server_enabled(self):
        """Toggle enabled status of selected server."""
        selection = self.servers_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a server to toggle")
            return
        messagebox.showinfo("Toggle Server", "Server enabled status would be toggled here")
    
    def get_conversation_stats(self):
        """Get conversation statistics."""
        try:
            conversations_file = Path("data/conversations.json")
            if conversations_file.exists():
                with open(conversations_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    conv_count = len(data)
                    total_messages = sum(len(conv.get('messages', [])) for conv in data)
                    return f"Conversations: {conv_count}\nTotal Messages: {total_messages}"
            else:
                return "No conversations found"
        except Exception as e:
            return f"Error loading stats: {str(e)}"
    
    def clear_conversations(self):
        """Clear all conversations."""
        if messagebox.askyesno("Confirm Clear", 
                              "Are you sure you want to clear all conversations?\n\nThis action cannot be undone."):
            messagebox.showinfo("Clear Conversations", "All conversations would be cleared here")
    
    def export_conversations(self):
        """Export conversations."""
        file_path = filedialog.asksaveasfilename(
            title="Export Conversations",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            messagebox.showinfo("Export Conversations", f"Conversations would be exported to {file_path}")
    
    def reset_to_defaults(self):
        """Reset all settings to defaults."""
        if messagebox.askyesno("Confirm Reset", 
                              "Are you sure you want to reset all settings to defaults?\n\nThis action cannot be undone."):
            messagebox.showinfo("Reset Settings", "Settings would be reset to defaults here")
    
    def collect_all_settings(self):
        """Collect all current settings."""
        return {
            'auto_save': self.auto_save_var.get(),
            'auto_start_mcp': self.auto_start_mcp_var.get(),
            'data_dir': self.data_dir_var.get(),
            'theme': self.theme_var.get(),
            'max_context_length': self.context_length_var.get(),
            'remember_context': self.remember_context_var.get(),
            'auto_summarize': self.auto_summarize_var.get(),
            'log_level': self.log_level_var.get(),
            'debug_mode': self.debug_mode_var.get(),
            'request_timeout': self.timeout_var.get()
        }
    
    def apply_imported_settings(self, settings):
        """Apply imported settings to the UI."""
        if 'auto_save' in settings:
            self.auto_save_var.set(settings['auto_save'])
        if 'auto_start_mcp' in settings:
            self.auto_start_mcp_var.set(settings['auto_start_mcp'])
        if 'data_dir' in settings:
            self.data_dir_var.set(settings['data_dir'])
        if 'theme' in settings:
            self.theme_var.set(settings['theme'])
        if 'max_context_length' in settings:
            self.context_length_var.set(settings['max_context_length'])
        if 'remember_context' in settings:
            self.remember_context_var.set(settings['remember_context'])
        if 'auto_summarize' in settings:
            self.auto_summarize_var.set(settings['auto_summarize'])
        if 'log_level' in settings:
            self.log_level_var.set(settings['log_level'])
        if 'debug_mode' in settings:
            self.debug_mode_var.set(settings['debug_mode'])
        if 'request_timeout' in settings:
            self.timeout_var.set(settings['request_timeout'])
    
    def apply_settings(self):
        """Apply current settings."""
        try:
            settings = self.collect_all_settings()
            # Apply settings to the application
            messagebox.showinfo("Settings Applied", "Settings have been applied successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply settings:\n{str(e)}")
    
    def ok_settings(self):
        """Apply settings and close window."""
        self.apply_settings()
        self.destroy()
    
    def cancel_settings(self):
        """Cancel settings and close window."""
        self.destroy()
