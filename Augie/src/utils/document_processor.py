"""
Document processing module for handling various file types.
Supports text, PDF, and image files with OCR capabilities.
"""
import logging
import os
import mimetypes
from typing import Optional, Dict, Any, List, Union, BinaryIO
from pathlib import Path

# Optional imports for document processing
try:
    import PyPDF2
    from pdf2image import convert_from_path
    from PIL import Image
    import pytesseract
    HAS_IMAGE_DEPS = True
except ImportError:
    HAS_IMAGE_DEPS = False

try:
    import docx2txt
    HAS_DOCX_DEPS = True
except ImportError:
    HAS_DOCX_DEPS = False

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """Process various document types to extract text content."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the document processor.
        
        Args:
            config: Configuration dictionary with processing options
        """
        self.config = config or {}
        self.supported_formats = self._get_supported_formats()
        
        # Configure Tesseract path if specified in config
        self.tesseract_cmd = self.config.get('tesseract_cmd')
        if self.tesseract_cmd and HAS_IMAGE_DEPS:
            pytesseract.pytesseract.tesseract_cmd = self.tesseract_cmd
    
    def _get_supported_formats(self) -> Dict[str, List[str]]:
        """Get a dictionary of supported file formats and their extensions."""
        return {
            'text': ['.txt', '.md', '.markdown', '.csv', '.json', '.yaml', '.yml', '.html', '.htm', '.xml'],
            'pdf': ['.pdf'],
            'image': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'],
            'word': ['.docx', '.doc'],
        }
    
    def get_file_type(self, file_path: Union[str, Path]) -> Optional[str]:
        """Determine the file type based on extension and content.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File type as a string ('text', 'pdf', 'image', 'word') or None if unsupported
        """
        if isinstance(file_path, str):
            file_path = Path(file_path)
        
        # First check by extension
        ext = file_path.suffix.lower()
        
        for file_type, extensions in self.supported_formats.items():
            if ext in extensions:
                return file_type
        
        # If extension check fails, try with mimetypes
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type:
            if mime_type.startswith('text/'):
                return 'text'
            elif mime_type == 'application/pdf':
                return 'pdf'
            elif mime_type.startswith('image/'):
                return 'image'
            elif mime_type in ('application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'):
                return 'word'
        
        return None
    
    def process_document(self, file_path: Union[str, Path], **kwargs) -> str:
        """Process a document and return its text content.
        
        Args:
            file_path: Path to the document file
            **kwargs: Additional processing options
            
        Returns:
            Extracted text content
            
        Raises:
            ValueError: If the file type is not supported or processing fails
            FileNotFoundError: If the file does not exist
        """
        if isinstance(file_path, str):
            file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_type = self.get_file_type(file_path)
        
        if not file_type:
            raise ValueError(f"Unsupported file type: {file_path.suffix}")
        
        # Process based on file type
        if file_type == 'text':
            return self._process_text_file(file_path, **kwargs)
        elif file_type == 'pdf':
            return self._process_pdf(file_path, **kwargs)
        elif file_type == 'image':
            return self._process_image(file_path, **kwargs)
        elif file_type == 'word':
            return self._process_word_document(file_path, **kwargs)
        else:
            raise ValueError(f"No processor available for file type: {file_type}")
    
    def _process_text_file(self, file_path: Path, **kwargs) -> str:
        """Process a plain text file."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading text file {file_path}: {e}")
            raise
    
    def _process_pdf(self, file_path: Path, use_ocr: bool = False, **kwargs) -> str:
        """Process a PDF file, extracting text from both text layers and optionally OCR."""
        if not HAS_IMAGE_DEPS:
            raise ImportError("Required PDF processing libraries not installed. Install with: pip install PyPDF2 pdf2image pillow pytesseract")
        
        text_content = []
        
        # First try to extract text from the PDF directly
        try:
            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                for page in reader.pages:
                    page_text = page.extract_text() or ""
                    text_content.append(page_text.strip())
            
            # If we got some text, return it
            if any(text_content):
                return "\n\n".join(text_content)
                
        except Exception as e:
            logger.warning(f"Error extracting text from PDF {file_path}: {e}")
        
        # If direct text extraction failed or returned empty, try OCR if enabled
        if use_ocr:
            try:
                # Convert PDF to images
                images = convert_from_path(file_path)
                
                # Process each page with OCR
                for i, image in enumerate(images):
                    page_text = self._process_image_with_ocr(image)
                    if page_text:
                        text_content.append(page_text)
                
                return "\n\n".join(text_content) if text_content else ""
                
            except Exception as e:
                logger.error(f"Error during PDF OCR processing {file_path}: {e}")
                raise
        
        return "\n\n".join(text_content) if text_content else ""
    
    def _process_image(self, file_path: Path, **kwargs) -> str:
        """Process an image file with OCR."""
        if not HAS_IMAGE_DEPS:
            raise ImportError("Required image processing libraries not installed. Install with: pip install pillow pytesseract")
        
        try:
            # Open the image file
            with Image.open(file_path) as img:
                return self._process_image_with_ocr(img)
                
        except Exception as e:
            logger.error(f"Error processing image {file_path}: {e}")
            raise
    
    def _process_image_with_ocr(self, image: 'Image.Image') -> str:
        """Process an image with OCR."""
        try:
            # Convert to grayscale for better OCR
            if image.mode != 'L':
                image = image.convert('L')
                
            # Use pytesseract to do OCR on the image
            return pytesseract.image_to_string(image).strip()
            
        except Exception as e:
            logger.error(f"Error during OCR processing: {e}")
            return ""
    
    def _process_word_document(self, file_path: Path, **kwargs) -> str:
        """Process a Word document (.docx or .doc)."""
        if not HAS_DOCX_DEPS:
            raise ImportError("Required Word document processing libraries not installed. Install with: pip install python-docx")
        
        try:
            return docx2txt.process(file_path)
        except Exception as e:
            logger.error(f"Error processing Word document {file_path}: {e}")
            raise
    
    def get_document_metadata(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Get metadata about a document.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Dictionary containing document metadata
        """
        if isinstance(file_path, str):
            file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_type = self.get_file_type(file_path)
        
        metadata = {
            'file_name': file_path.name,
            'file_path': str(file_path.absolute()),
            'file_size': file_path.stat().st_size,
            'file_type': file_type,
            'file_extension': file_path.suffix.lower(),
            'created': file_path.stat().st_ctime,
            'modified': file_path.stat().st_mtime,
        }
        
        # Add type-specific metadata
        if file_type == 'pdf' and HAS_IMAGE_DEPS:
            try:
                with open(file_path, 'rb') as f:
                    reader = PyPDF2.PdfReader(f)
                    metadata.update({
                        'page_count': len(reader.pages),
                        'is_encrypted': reader.is_encrypted,
                        'pdf_metadata': reader.metadata or {}
                    })
            except Exception as e:
                logger.warning(f"Could not extract PDF metadata: {e}")
        
        elif file_type == 'image' and HAS_IMAGE_DEPS:
            try:
                with Image.open(file_path) as img:
                    metadata.update({
                        'width': img.width,
                        'height': img.height,
                        'format': img.format,
                        'mode': img.mode,
                    })
            except Exception as e:
                logger.warning(f"Could not extract image metadata: {e}")
        
        return metadata

# Global instance for convenience
document_processor = DocumentProcessor()

def process_document(file_path: Union[str, Path], **kwargs) -> str:
    """Convenience function to process a document."""
    return document_processor.process_document(file_path, **kwargs)
