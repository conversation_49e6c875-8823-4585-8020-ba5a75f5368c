"""
Speech-to-Text (STT) module for converting spoken words to text.
Uses the SpeechRecognition library with fallback to offline recognition.
"""
import asyncio
import logging
import queue
import threading
from dataclasses import dataclass
from typing import Optional, Callable, Awaitable

import speech_recognition as sr
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class RecognitionResult(BaseModel):
    """Result of a speech recognition operation."""
    text: str
    is_wake_word: bool = False
    error: Optional[str] = None

class SpeechToText:
    """Handles speech recognition with wake word detection."""
    
    def __init__(self, 
                 wake_word: str = "hey assistant",
                 energy_threshold: int = 4000,
                 pause_threshold: float = 0.8,
                 dynamic_energy_threshold: bool = True):
        """Initialize the speech recognizer.
        
        Args:
            wake_word: The wake word/phrase to listen for
            energy_threshold: Energy level for mic to consider as speech
            pause_threshold: Seconds of silence to end speech input
            dynamic_energy_threshold: Whether to adjust threshold based on ambient noise
        """
        self.recognizer = sr.Recognizer()
        self.recognizer.energy_threshold = energy_threshold
        self.recognizer.pause_threshold = pause_threshold
        self.recognizer.dynamic_energy_threshold = dynamic_energy_threshold
        
        self.wake_word = wake_word.lower()
        self.audio_queue = asyncio.Queue()
        self.stop_listening = threading.Event()
        self.callback = None
        self.background_task = None
        
        # Adjust for ambient noise
        with sr.Microphone() as source:
            logger.info("Adjusting for ambient noise...")
            self.recognizer.adjust_for_ambient_noise(source, duration=1)
            logger.info(f"Energy threshold set to: {self.recognizer.energy_threshold}")
    
    async def _recognize_google(self, audio_data) -> Optional[str]:
        """Recognize speech using Google Web Speech API."""
        try:
            return self.recognizer.recognize_google(audio_data)
        except sr.UnknownValueError:
            logger.debug("Google Speech Recognition could not understand audio")
        except sr.RequestError as e:
            logger.error(f"Could not request results from Google Speech Recognition service; {e}")
        return None
    
    async def _recognize_sphinx(self, audio_data) -> Optional[str]:
        """Recognize speech using CMU Sphinx (offline)."""
        try:
            return self.recognizer.recognize_sphinx(audio_data)
        except sr.UnknownValueError:
            logger.debug("Sphinx could not understand audio")
        except sr.RequestError as e:
            logger.error(f"Sphinx error; {e}")
        return None
    
    async def _process_audio(self, audio_data) -> Optional[RecognitionResult]:
        """Process audio data and return recognized text."""
        try:
            # Try Google's speech recognition first (requires internet)
            text = await self._recognize_google(audio_data)
            
            # Fall back to Sphinx if Google fails
            if not text:
                text = await self._recognize_sphinx(audio_data)
            
            if text:
                text = text.lower().strip()
                is_wake_word = self.wake_word in text
                
                # If wake word is detected, remove it from the text
                if is_wake_word:
                    text = text.replace(self.wake_word, "").strip()
                    
                return RecognitionResult(
                    text=text,
                    is_wake_word=is_wake_word
                )
                
        except Exception as e:
            logger.error(f"Error processing audio: {e}")
            return RecognitionResult(
                text="",
                error=str(e)
            )
        
        return None
    
    async def listen(self, timeout: Optional[float] = None) -> Optional[str]:
        """Listen for speech and return the recognized text.
        
        Args:
            timeout: Maximum time to listen in seconds
            
        Returns:
            Recognized text or None if nothing was recognized
        """
        with sr.Microphone() as source:
            logger.info("Listening...")
            try:
                audio_data = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: self.recognizer.listen(source, timeout=timeout, phrase_time_limit=5)
                )
                result = await self._process_audio(audio_data)
                return result.text if result else None
                
            except sr.WaitTimeoutError:
                logger.debug("Listening timed out")
                return None
            except Exception as e:
                logger.error(f"Error in listen: {e}")
                return None
    
    async def listen_for_wake_word(self, timeout: Optional[float] = None) -> bool:
        """Listen continuously until the wake word is detected.
        
        Args:
            timeout: Maximum time to listen in seconds
            
        Returns:
            bool: True if wake word was detected, False if timed out
        """
        logger.info(f"Listening for wake word: '{self.wake_word}'")
        start_time = asyncio.get_event_loop().time()
        
        while True:
            if timeout:
                elapsed = asyncio.get_event_loop().time() - start_time
                if elapsed >= timeout:
                    return False
                
            text = await self.listen(timeout=1)  # Listen in 1-second chunks
            
            if text and self.wake_word in text.lower():
                logger.info(f"Wake word detected!")
                return True
    
    def start_background_listening(self, callback: Callable[[RecognitionResult], Awaitable[None]]):
        """Start listening in the background and call the callback with results.
        
        Args:
            callback: Async function to call with recognition results
        """
        if self.background_task and not self.background_task.done():
            logger.warning("Background listening already active")
            return
            
        self.callback = callback
        self.stop_listening.clear()
        self.background_task = asyncio.create_task(self._background_loop())
    
    async def stop_background_listening(self):
        """Stop background listening."""
        if self.background_task:
            self.stop_listening.set()
            self.background_task.cancel()
            try:
                await self.background_task
            except asyncio.CancelledError:
                pass
            self.background_task = None
    
    async def _background_loop(self):
        """Background loop for continuous listening."""
        while not self.stop_listening.is_set():
            try:
                text = await self.listen(timeout=1)
                if text and self.callback:
                    result = await self._process_audio(text)
                    if result and (result.is_wake_word or result.text):
                        await self.callback(result)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in background loop: {e}")
                await asyncio.sleep(1)  # Prevent tight loop on error
