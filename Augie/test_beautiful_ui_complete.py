#!/usr/bin/env python3
"""
Complete test of the beautiful UI design with all tabs.
Inspired by the favorite UI screenshots.
"""
import tkinter as tk
from tkinter import ttk, messagebox
import datetime

class BeautifulUIComplete:
    """Complete demo of the beautiful UI with all tabs."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("The Printery AI Assistant")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        
        # The Printery theme colors
        self.colors = {
            'bg_primary': '#ffffff',
            'bg_secondary': '#fef7f7',
            'bg_tertiary': '#f0f8ff',
            'text_primary': '#000000',
            'text_secondary': '#4a4a4a',
            'accent': '#20B2AA',
            'accent_hover': '#1a9999',
            'success': '#28a745',
            'warning': '#ffc107',
            'danger': '#dc3545',
            'user_msg': '#FFB6C1',
            'assistant_msg': '#E0F8F8',
            'hover': '#f5f5f5',
            'printery_pink': '#FFB6C1',
            'printery_teal': '#20B2AA',
        }
        
        self.current_view = "home"
        self.current_right_panel = "topics"
        
        self.root.configure(bg=self.colors['bg_primary'])
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the complete beautiful UI."""
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Three-panel layout
        self.setup_left_sidebar(main_frame)
        self.setup_main_content(main_frame)
        self.setup_right_panel(main_frame)
    
    def setup_left_sidebar(self, parent):
        """Set up the left sidebar with all navigation tabs."""
        # Left sidebar
        left_sidebar = tk.Frame(
            parent,
            bg=self.colors['bg_secondary'],
            width=280
        )
        left_sidebar.pack(side=tk.LEFT, fill=tk.Y)
        left_sidebar.pack_propagate(False)
        
        # Header
        header_frame = tk.Frame(left_sidebar, bg=self.colors['bg_secondary'])
        header_frame.pack(fill=tk.X, padx=15, pady=15)
        
        # The Printery logo
        logo_frame = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        logo_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(
            logo_frame,
            text="🍒 The Printery",
            font=('Arial', 16, 'bold'),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        ).pack(side=tk.LEFT)
        
        # Main navigation tabs (Home, Agents, Files, Knowledge Base, Translate, Apps)
        nav_frame = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        nav_frame.pack(fill=tk.X, pady=(0, 15))
        
        # First row
        nav_row1 = tk.Frame(nav_frame, bg=self.colors['bg_secondary'])
        nav_row1.pack(fill=tk.X, pady=(0, 5))
        
        self.create_nav_button(nav_row1, "🏠", "Home", "home", True)
        self.create_nav_button(nav_row1, "🤖", "Agents", "agents")
        self.create_nav_button(nav_row1, "📁", "Files", "files")
        
        # Second row
        nav_row2 = tk.Frame(nav_frame, bg=self.colors['bg_secondary'])
        nav_row2.pack(fill=tk.X)
        
        self.create_nav_button(nav_row2, "📚", "Knowledge Base", "knowledge")
        self.create_nav_button(nav_row2, "🌐", "Translate", "translate")
        self.create_nav_button(nav_row2, "📱", "Apps", "apps")
        
        # Current assistant section
        self.setup_assistant_section(left_sidebar)
        
        # Agent categories (from the screenshot)
        self.setup_agent_categories(left_sidebar)
    
    def create_nav_button(self, parent, icon, text, view, active=False):
        """Create a navigation button."""
        bg_color = self.colors['accent'] if active else self.colors['bg_tertiary']
        fg_color = 'white' if active else self.colors['text_primary']
        font_weight = 'bold' if active else 'normal'
        
        btn = tk.Button(
            parent,
            text=f"{icon} {text}",
            bg=bg_color,
            fg=fg_color,
            border=0,
            font=('Arial', 9, font_weight),
            cursor='hand2',
            command=lambda: self.switch_view(view)
        )
        btn.pack(side=tk.LEFT, padx=(0, 3))
        return btn
    
    def setup_assistant_section(self, parent):
        """Set up the assistant section."""
        assistant_section = tk.Frame(parent, bg=self.colors['bg_secondary'])
        assistant_section.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        # Assistant info
        assistant_info_frame = tk.Frame(assistant_section, bg=self.colors['bg_secondary'])
        assistant_info_frame.pack(fill=tk.X)
        
        # Avatar
        avatar_frame = tk.Frame(assistant_info_frame, bg=self.colors['bg_secondary'])
        avatar_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        avatar_label = tk.Label(
            avatar_frame,
            text="🤖",
            font=('Arial', 20),
            bg=self.colors['accent'],
            fg='white',
            width=3,
            height=1
        )
        avatar_label.pack()
        
        # Details
        details_frame = tk.Frame(assistant_info_frame, bg=self.colors['bg_secondary'])
        details_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        tk.Label(
            details_frame,
            text="Default Assistant",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            anchor=tk.W
        ).pack(fill=tk.X)
        
        tk.Label(
            details_frame,
            text="1 conversation",
            font=('Arial', 9),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            anchor=tk.W
        ).pack(fill=tk.X)
        
        # Add Assistant button
        add_assistant_btn = tk.Button(
            assistant_section,
            text="+ Add Assistant",
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            border=0,
            font=('Arial', 10),
            cursor='hand2'
        )
        add_assistant_btn.pack(fill=tk.X, pady=(10, 0))
    
    def setup_agent_categories(self, parent):
        """Set up agent categories from the screenshot."""
        categories_frame = tk.Frame(parent, bg=self.colors['bg_secondary'])
        categories_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        # My Agents section
        tk.Label(
            categories_frame,
            text="My Agents",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            anchor=tk.W
        ).pack(fill=tk.X, pady=(0, 10))
        
        # Agent categories from screenshot
        categories = [
            ("⭐", "Featured", "6"),
            ("💼", "Career", "78"),
            ("🏢", "Business", "102"),
            ("🌍", "Language", "12"),
            ("📊", "Office", "45"),
            ("✍️", "Writing", "89"),
            ("⭐", "Featured", "6"),
            ("💻", "Programming", "34"),
            ("😊", "Emotion", "23"),
            ("🎓", "Education", "67"),
            ("🎨", "Creative", "56"),
            ("📚", "Academic", "43"),
            ("🎨", "Design", "29"),
            ("🎭", "Art", "18"),
            ("🎬", "Entertainment", "41"),
            ("🌱", "Life", "52"),
            ("⚕️", "Medical", "31"),
            ("🎮", "Games", "25"),
            ("🌐", "Translation", "15")
        ]
        
        for icon, name, count in categories:
            cat_frame = tk.Frame(categories_frame, bg=self.colors['bg_secondary'])
            cat_frame.pack(fill=tk.X, pady=1)
            
            cat_btn = tk.Button(
                cat_frame,
                text=f"{icon} {name}",
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary'],
                border=0,
                font=('Arial', 10),
                anchor=tk.W,
                cursor='hand2',
                command=lambda n=name: self.show_category(n)
            )
            cat_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=2)
            
            # Count label
            count_label = tk.Label(
                cat_frame,
                text=count,
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_secondary'],
                font=('Arial', 9)
            )
            count_label.pack(side=tk.RIGHT, padx=5)
            
            # Hover effect
            def on_enter(e, btn=cat_btn):
                btn.config(bg=self.colors['hover'])
            
            def on_leave(e, btn=cat_btn):
                btn.config(bg=self.colors['bg_secondary'])
            
            cat_btn.bind("<Enter>", on_enter)
            cat_btn.bind("<Leave>", on_leave)
    
    def setup_main_content(self, parent):
        """Set up the main content area."""
        # Main content frame
        content_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        content_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Header bar
        header_frame = tk.Frame(
            content_frame,
            bg=self.colors['bg_primary'],
            height=60
        )
        header_frame.pack(fill=tk.X, padx=20, pady=(10, 0))
        header_frame.pack_propagate(False)
        
        # Model info
        model_frame = tk.Frame(header_frame, bg=self.colors['bg_primary'])
        model_frame.pack(side=tk.LEFT, fill=tk.Y)
        
        # Model icon
        model_icon = tk.Label(
            model_frame,
            text="🧠",
            font=('Arial', 16),
            bg=self.colors['accent'],
            fg='white',
            width=2,
            height=1
        )
        model_icon.pack(side=tk.LEFT, padx=(0, 10))
        
        # Model details
        model_details = tk.Frame(model_frame, bg=self.colors['bg_primary'])
        model_details.pack(side=tk.LEFT, fill=tk.Y)
        
        tk.Label(
            model_details,
            text="The Printery AI Assistant | Ready to Help",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary'],
            anchor=tk.W
        ).pack(anchor=tk.W)
        
        tk.Label(
            model_details,
            text="Hello, I'm your AI assistant. You can start chatting with me right away",
            font=('Arial', 10),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary'],
            anchor=tk.W
        ).pack(anchor=tk.W)
        
        # Right side buttons
        buttons_frame = tk.Frame(header_frame, bg=self.colors['bg_primary'])
        buttons_frame.pack(side=tk.RIGHT)
        
        # Search button
        search_btn = tk.Button(
            buttons_frame,
            text="🔍 Search",
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            border=0,
            font=('Arial', 9),
            cursor='hand2'
        )
        search_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Import button
        import_btn = tk.Button(
            buttons_frame,
            text="📥 Import from External",
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            border=0,
            font=('Arial', 9),
            cursor='hand2'
        )
        import_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Create Agent button
        create_btn = tk.Button(
            buttons_frame,
            text="+ Create Agent",
            bg=self.colors['accent'],
            fg='white',
            border=0,
            font=('Arial', 9, 'bold'),
            cursor='hand2'
        )
        create_btn.pack(side=tk.LEFT)
        
        # Main content area
        self.setup_main_content_area(content_frame)
    
    def setup_main_content_area(self, parent):
        """Set up the main content area based on current view."""
        # Content container
        content_container = tk.Frame(parent, bg=self.colors['bg_primary'])
        content_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        if self.current_view == "home":
            self.setup_home_view(content_container)
        elif self.current_view == "agents":
            self.setup_agents_view(content_container)
        else:
            self.setup_default_view(content_container)
    
    def setup_home_view(self, parent):
        """Set up the home/chat view."""
        # Sample messages
        self.add_sample_messages(parent)
        
        # Input area
        self.setup_input_area(parent)
    
    def setup_agents_view(self, parent):
        """Set up the agents view."""
        # Agents grid or list would go here
        tk.Label(
            parent,
            text="My Agents",
            font=('Arial', 18, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        ).pack(pady=50)
        
        # No results found (like in screenshot)
        no_results_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        no_results_frame.pack(expand=True)
        
        tk.Label(
            no_results_frame,
            text="📋",
            font=('Arial', 48),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary']
        ).pack()
        
        tk.Label(
            no_results_frame,
            text="No results found",
            font=('Arial', 14),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary']
        ).pack(pady=(10, 0))
    
    def setup_default_view(self, parent):
        """Set up default view for other tabs."""
        tk.Label(
            parent,
            text=f"{self.current_view.title()} View",
            font=('Arial', 18, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        ).pack(pady=50)
        
        tk.Label(
            parent,
            text=f"This would show the {self.current_view} interface",
            font=('Arial', 12),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary']
        ).pack()
    
    def add_sample_messages(self, parent):
        """Add sample messages."""
        messages_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        messages_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Sample conversation
        sample_messages = [
            ("user", "Hello! I'd like to learn about The Printery's services."),
            ("assistant", "Welcome to The Printery! We're a full-service print shop specializing in high-quality printing, design services, and custom solutions. We offer business cards, brochures, banners, wedding invitations, and much more. How can I help you today?"),
            ("user", "Can you help me design a business card?"),
            ("assistant", "Absolutely! I'd be happy to help you design a professional business card. Let's start with some questions:\n\n• What's your business name and your role?\n• What contact information do you want to include?\n• Do you have any specific colors or branding preferences?\n• What style are you looking for (modern, classic, creative)?\n\nOnce I have these details, I can suggest some design concepts that would work well for The Printery's printing capabilities.")
        ]
        
        for role, content in sample_messages:
            self.add_message_bubble(messages_frame, role, content)
    
    def add_message_bubble(self, parent, role, content):
        """Add a message bubble."""
        msg_container = tk.Frame(parent, bg=self.colors['bg_primary'])
        msg_container.pack(fill=tk.X, pady=8)
        
        if role == 'user':
            anchor = tk.E
            bg_color = self.colors['user_msg']
            text_color = 'black'
            padx = (150, 20)
        else:
            anchor = tk.W
            bg_color = self.colors['assistant_msg']
            text_color = 'black'
            padx = (20, 150)
        
        bubble_frame = tk.Frame(msg_container, bg=self.colors['bg_primary'])
        bubble_frame.pack(anchor=anchor, padx=padx)
        
        msg_label = tk.Label(
            bubble_frame,
            text=content,
            bg=bg_color,
            fg=text_color,
            font=('Arial', 11),
            wraplength=450,
            justify=tk.LEFT,
            anchor=tk.W,
            relief=tk.SOLID,
            bd=1
        )
        msg_label.pack(padx=2, pady=2)
        
        time_label = tk.Label(
            msg_container,
            text="14:25",
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 8)
        )
        time_label.pack(anchor=anchor, padx=padx)
    
    def setup_input_area(self, parent):
        """Set up the input area."""
        # Input container
        input_container = tk.Frame(
            parent,
            bg=self.colors['bg_primary'],
            height=120
        )
        input_container.pack(fill=tk.X)
        input_container.pack_propagate(False)
        
        # Input frame
        input_frame = tk.Frame(
            input_container,
            bg=self.colors['bg_tertiary'],
            relief=tk.SOLID,
            bd=1
        )
        input_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Text input
        input_text = tk.Text(
            input_frame,
            height=4,
            wrap=tk.WORD,
            font=('Arial', 11),
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            border=0,
            relief=tk.FLAT
        )
        input_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        input_text.insert('1.0', "Type your message here, press Enter to send...")
        
        # Toolbar
        toolbar_frame = tk.Frame(input_frame, bg=self.colors['bg_tertiary'])
        toolbar_frame.pack(fill=tk.X, padx=15, pady=(0, 10))
        
        # Left buttons
        left_buttons = tk.Frame(toolbar_frame, bg=self.colors['bg_tertiary'])
        left_buttons.pack(side=tk.LEFT)
        
        for icon in ["📎", "🌐", "😊", "📷", "🎤"]:
            btn = tk.Button(
                left_buttons,
                text=icon,
                bg=self.colors['bg_tertiary'],
                fg=self.colors['text_primary'],
                border=0,
                font=('Arial', 12),
                cursor='hand2'
            )
            btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Right buttons
        right_buttons = tk.Frame(toolbar_frame, bg=self.colors['bg_tertiary'])
        right_buttons.pack(side=tk.RIGHT)
        
        send_btn = tk.Button(
            right_buttons,
            text="➤",
            bg=self.colors['accent'],
            fg='white',
            border=0,
            font=('Arial', 14, 'bold'),
            cursor='hand2',
            width=3,
            command=lambda: messagebox.showinfo("Send", "Message would be sent!")
        )
        send_btn.pack(side=tk.RIGHT)
    
    def setup_right_panel(self, parent):
        """Set up the right panel."""
        # Right panel
        right_panel = tk.Frame(
            parent,
            bg=self.colors['bg_secondary'],
            width=300
        )
        right_panel.pack(side=tk.RIGHT, fill=tk.Y)
        right_panel.pack_propagate(False)
        
        # Header with tabs
        header_frame = tk.Frame(right_panel, bg=self.colors['bg_secondary'])
        header_frame.pack(fill=tk.X, padx=15, pady=15)
        
        # Tab buttons
        tabs_frame = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        tabs_frame.pack(fill=tk.X)
        
        # Topics tab (active)
        self.topics_tab_btn = tk.Button(
            tabs_frame,
            text="Topics",
            bg=self.colors['accent'],
            fg='white',
            border=0,
            font=('Arial', 11, 'bold'),
            cursor='hand2',
            command=lambda: self.switch_right_panel("topics")
        )
        self.topics_tab_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Settings tab
        self.settings_tab_btn = tk.Button(
            tabs_frame,
            text="Settings",
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            border=0,
            font=('Arial', 11),
            cursor='hand2',
            command=lambda: self.switch_right_panel("settings")
        )
        self.settings_tab_btn.pack(side=tk.LEFT)
        
        # Panel content
        self.right_panel_content = tk.Frame(right_panel, bg=self.colors['bg_secondary'])
        self.right_panel_content.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        # Show topics by default
        self.setup_topics_panel()
    
    def update_topic_list(self):
        """Update the topic list display."""
        # Clear previous topic list
        for widget in self.topic_list_frame.winfo_children():
            widget.destroy()
        
        # Create new topic list
        for index, topic in enumerate(self.topics):
            topic_frame = tk.Frame(self.topic_list_frame, bg=self.colors['bg_secondary'])
            topic_frame.pack(fill=tk.X, pady=2)
            
            tk.Label(
                topic_frame,
                text=topic.title,
                font=('Arial', 10),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']
            ).pack(side=tk.LEFT, padx=10)
            
            # Rename button
            rename_btn = tk.Button(
                topic_frame,
                text="✏️",
                bg=self.colors['accent'],
                fg='white',
                font=('Arial', 8),
                command=lambda idx=index: self.rename_topic_prompt(idx)
            )
            rename_btn.pack(side=tk.RIGHT, padx=5)
            
            # Delete button
            delete_btn = tk.Button(
                topic_frame,
                text="🗑️",
                bg=self.colors['warning'],
                fg='white',
                font=('Arial', 8),
                command=lambda idx=index: self.delete_topic(idx)
            )
            delete_btn.pack(side=tk.RIGHT, padx=5)

    def rename_topic(self, topic_index, new_title):
        """Rename a topic."""
        if 0 <= topic_index < len(self.topics):
            self.topics[topic_index].title = new_title
            self.update_topic_list()

    def delete_topic(self, topic_index):
        """Delete a topic."""
        if 0 <= topic_index < len(self.topics):
            del self.topics[topic_index]
            self.update_topic_list()

    def rename_topic_prompt(self, topic_index):
        """Show rename prompt for a topic."""
        # Create a new top-level window for renaming
        rename_window = tk.Toplevel(self.root)
        rename_window.title("Rename Topic")
        
        tk.Label(rename_window, text="Enter new topic title:").pack()
        
        new_title_entry = tk.Entry(rename_window)
        new_title_entry.pack()
        
        def save_new_title():
            new_title = new_title_entry.get()
            self.rename_topic(topic_index, new_title)
            rename_window.destroy()
        
        tk.Button(rename_window, text="Save", command=save_new_title).pack()

    def setup_topics_panel(self):
        """Set up the topics panel."""
        # Clear content
        for widget in self.right_panel_content.winfo_children():
            widget.destroy()
        
        # New Topic button
        new_topic_btn = tk.Button(
            self.right_panel_content,
            text="+ New Topic",
            bg=self.colors['accent'],
            fg='white',
            border=0,
            font=('Arial', 10, 'bold'),
            cursor='hand2'
        )
        new_topic_btn.pack(fill=tk.X, pady=(0, 15))
        
        # Sample Topics
        sample_topics = [
            "Default Topic",
            "Business Discussion",
            "Personal Planning"
        ]
        
        self.topics = []
        self.topic_list_frame = tk.Frame(self.right_panel_content, bg=self.colors['bg_secondary'])
        self.topic_list_frame.pack(fill=tk.BOTH, expand=True)
        
        for topic_title in sample_topics:
            topic = type('Topic', (object,), {
                'title': topic_title,
                'messages': []
            })
            self.topics.append(topic)
        
        self.update_topic_list()

def update_topic_list(self):
    """Update the topic list display."""
    # Clear previous topic list
    for widget in self.topic_list_frame.winfo_children():
        widget.destroy()
    
    # Create new topic list
    for index, topic in enumerate(self.topics):
        topic_frame = tk.Frame(self.topic_list_frame, bg=self.colors['bg_secondary'])
        topic_frame.pack(fill=tk.X, pady=2)
        
        tk.Label(
            topic_frame,
            text=topic.title,
            font=('Arial', 10),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        ).pack(side=tk.LEFT, padx=10)
        
        # Rename button
        rename_btn = tk.Button(
            topic_frame,
            text="✏️",
            bg=self.colors['accent'],
            fg='white',
            font=('Arial', 8),
            command=lambda idx=index: self.rename_topic_prompt(idx)
        )
        rename_btn.pack(side=tk.RIGHT, padx=5)
        
        # Delete button
        delete_btn = tk.Button(
            topic_frame,
            text="🗑️",
            bg=self.colors['warning'],
            fg='white',
            font=('Arial', 8),
            command=lambda idx=index: self.delete_topic(idx)
        )
        delete_btn.pack(side=tk.RIGHT, padx=5)

def rename_topic(self, topic_index, new_title):
    """Rename a topic."""
    if 0 <= topic_index < len(self.topics):
        self.topics[topic_index].title = new_title
        self.update_topic_list()

def delete_topic(self, topic_index):
    """Delete a topic."""
    if 0 <= topic_index < len(self.topics):
        del self.topics[topic_index]
        self.update_topic_list()

def rename_topic_prompt(self, topic_index):
    """Show rename prompt for a topic."""
    # Create a new top-level window for renaming
    rename_window = tk.Toplevel(self.root)
    rename_window.title("Rename Topic")
    
    tk.Label(rename_window, text="Enter new topic title:").pack()
    
    new_title_entry = tk.Entry(rename_window)
    new_title_entry.pack()
    
    def save_new_title():
        new_title = new_title_entry.get()
        self.rename_topic(topic_index, new_title)
        rename_window.destroy()
    
    tk.Button(rename_window, text="Save", command=save_new_title).pack()
    
    def switch_view(self, view):
        """Switch between different main views."""
        self.current_view = view
        
        # Refresh the main content
        for widget in self.root.winfo_children():
            widget.destroy()
        
        self.setup_ui()
        
        messagebox.showinfo("View Switch", f"Switched to {view.title()} view")
    
    def switch_right_panel(self, panel):
        """Switch right panel between topics and settings."""
        self.current_right_panel = panel
        
        if panel == "topics":
            self.topics_tab_btn.config(bg=self.colors['accent'], fg='white', font=('Arial', 11, 'bold'))
            self.settings_tab_btn.config(bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'], font=('Arial', 11))
            self.setup_topics_panel()
        else:
            self.settings_tab_btn.config(bg=self.colors['accent'], fg='white', font=('Arial', 11, 'bold'))
            self.topics_tab_btn.config(bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'], font=('Arial', 11))
            self.setup_settings_panel()
    
    def setup_settings_panel(self):
        """Set up the settings panel."""
        # Clear content
        for widget in self.right_panel_content.winfo_children():
            widget.destroy()
        
        tk.Label(
            self.right_panel_content,
            text="Quick Settings",
            font=('Arial', 14, 'bold'),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        ).pack(pady=20)
        
        # Theme selector
        theme_frame = tk.Frame(self.right_panel_content, bg=self.colors['bg_secondary'])
        theme_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(
            theme_frame,
            text="Theme:",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            font=('Arial', 10)
        ).pack(side=tk.LEFT)
        
        theme_combo = ttk.Combobox(
            theme_frame,
            values=["The Printery Theme", "Dark Printery Theme"],
            state="readonly",
            width=15
        )
        theme_combo.set("The Printery Theme")
        theme_combo.pack(side=tk.RIGHT)
    
    def show_category(self, category):
        """Show a specific agent category."""
        messagebox.showinfo("Category", f"Showing {category} agents")
    
if __name__ == "__main__":
 print("🍒 The Printery AI Assistant - Complete Beautiful UI Demo")
 print("=" *70)
 print("Showing the complete beautiful UI with ALL tabs:")
 print("✓ Main Navigation: Home, Agents, Files, Knowledge Base, Translate, Apps")
 print("✓ Left Sidebar: Agent categories with counts")
 print("✓ Right Panel: Topics and Settings tabs")
 print("✓ The Printery branding throughout")
 print("✓ Professional message bubbles")
 print("✓ Modern input area with toolbar")
 print("✓ Search, Import, and Create Agent buttons")
 print("=" *70)
    
 demo = BeautifulUIComplete()
 demo.root.mainloop()
