#!/usr/bin/env python3
"""
Test script for the complete integrated chat system with conversation tree.
"""

import tkinter as tk
import sys
import os
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ui.modern_chat_window_v2 import ModernChatWindowV2

def test_complete_integration():
    """Test the complete integrated chat system."""
    print("🚀 Testing Complete Integration...")
    
    # Create root window
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    
    try:
        # Create the main chat window
        print("📱 Creating main chat window...")
        chat_window = ModernChatWindowV2(root)
        
        # Test basic functionality
        print("✅ Chat window created successfully")
        
        # Test conversation tree
        print("🌳 Testing conversation tree...")
        chat_window.conversation_tree.refresh()
        print("✅ Conversation tree loaded")
        
        # Test category manager
        print("📁 Testing category manager...")
        chat_window.category_manager.load_from_file()
        print("✅ Category manager loaded")
        
        # Test new conversation creation
        print("➕ Testing new conversation...")
        conv_id = chat_window.category_manager.create_conversation("Test Conversation")
        chat_window.category_manager.add_message(conv_id, {
            'content': 'Hello, this is a test message',
            'role': 'user',
            'timestamp': '2024-01-01T12:00:00'
        })
        chat_window.save_data()
        print("✅ New conversation created and saved")
        
        # Test search functionality
        print("🔍 Testing search...")
        results = chat_window.category_manager.search_conversations("test")
        print(f"✅ Search found {len(results)} results")
        
        # Test data persistence
        print("💾 Testing data persistence...")
        chat_window.save_data()
        chat_window.load_data()
        print("✅ Data persistence working")
        
        print("\n🎉 All integration tests passed!")
        print("The complete chat system is ready for use.")
        
        # Start the GUI event loop
        print("\n🖥️  Starting GUI...")
        chat_window.mainloop()
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_complete_integration()
    if success:
        print("\n✅ Integration test completed successfully!")
    else:
        print("\n❌ Integration test failed!")
        sys.exit(1)
