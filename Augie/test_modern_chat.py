#!/usr/bin/env python3
"""
Test script for the modern chat interface.
"""
import sys
import asyncio
import logging
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from src.assistant import VoiceAssistant
from src.ui.main_window import MainWindow
from src.utils.config import load_config
from src.utils.logger import setup_logging

async def main():
    """Main entry point for testing the modern chat interface."""
    print("🚀 Starting Cherry Studio Clone Test...")
    
    # Set up logging
    setup_logging(log_level='INFO', log_file='logs/test_modern_chat.log')
    logger = logging.getLogger(__name__)
    
    try:
        # Load configuration
        config = load_config('config_new.ini')
        logger.info("Configuration loaded successfully")
        
        # Initialize the assistant
        logger.info("Initializing assistant...")
        assistant = VoiceAssistant(config)
        await assistant.initialize()
        logger.info("Assistant initialized successfully")
        
        # Create and run the main window
        logger.info("Creating main window...")
        app = MainWindow(config, assistant)
        
        # Add a welcome message to the chat
        app.append_message("System", "🍒 Welcome to Cherry Studio Clone!", 'system')
        app.append_message("System", "Click the '🚀 Modern Chat' button to open the new interface!", 'system')
        
        logger.info("Starting application...")
        app.run()
        
    except KeyboardInterrupt:
        print("\n👋 Application terminated by user.")
    except Exception as e:
        print(f"❌ Fatal error: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
