#!/usr/bin/env python3
"""
Test script for Phase 2 implementation.
Tests The Printery branding, keyboard shortcuts, and MCP integration.
"""
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import tkinter as tk
from src.ui.themes import ThemeManager
from src.ui.modern_chat_window import ModernChatWindow
from src.mcp.server_manager import MCPServerManager
from src.mcp.basic_servers import validate_mcp_server_requirements, get_mcp_setup_instructions

class Phase2TestApp:
    """Test application for Phase 2 features."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Phase 2 Implementation Test")
        self.root.geometry("800x600")
        
        # Initialize managers
        self.theme_manager = ThemeManager()
        self.mcp_manager = MCPServerManager()
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the test UI."""
        # Main frame
        main_frame = tk.Frame(self.root, bg='white', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="🍒 The Printery AI Assistant - Phase 2 Test",
            font=('Arial', 16, 'bold'),
            bg='white',
            fg='#000000'
        )
        title_label.pack(pady=(0, 20))
        
        # Theme test section
        self.setup_theme_test(main_frame)
        
        # MCP test section
        self.setup_mcp_test(main_frame)
        
        # Launch modern chat button
        launch_btn = tk.Button(
            main_frame,
            text="🚀 Launch The Printery Chat Interface",
            font=('Arial', 12, 'bold'),
            bg='#20B2AA',  # The Printery teal
            fg='white',
            command=self.launch_modern_chat,
            cursor='hand2',
            pady=10
        )
        launch_btn.pack(pady=20, fill=tk.X)
        
        # Instructions
        instructions = tk.Text(
            main_frame,
            height=8,
            wrap=tk.WORD,
            font=('Arial', 10),
            bg='#f0f8ff'
        )
        instructions.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        instructions_text = """Phase 2 Implementation Test Instructions:

1. Theme Test: Verify The Printery theme is loaded with pink and teal colors
2. MCP Test: Check MCP server requirements and configuration
3. Launch Chat: Open the modern chat interface with:
   - The Printery branding and colors
   - Keyboard shortcuts (Ctrl+N, Ctrl+Enter, Escape, Ctrl+/)
   - Theme switching capability
   - MCP integration ready

Keyboard Shortcuts in Chat Window:
• Ctrl+N: New conversation
• Ctrl+Enter: Send message
• Escape: Focus input field
• Ctrl+/: Show help"""
        
        instructions.insert('1.0', instructions_text)
        instructions.config(state='disabled')
    
    def setup_theme_test(self, parent):
        """Set up theme testing section."""
        theme_frame = tk.LabelFrame(
            parent,
            text="Theme Test",
            font=('Arial', 12, 'bold'),
            bg='white',
            padx=10,
            pady=10
        )
        theme_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Current theme info
        current_theme = self.theme_manager.get_current_theme()
        
        theme_info = tk.Label(
            theme_frame,
            text=f"Current Theme: {current_theme.display_name}",
            font=('Arial', 10),
            bg='white'
        )
        theme_info.pack(anchor=tk.W)
        
        # Color samples
        colors_frame = tk.Frame(theme_frame, bg='white')
        colors_frame.pack(fill=tk.X, pady=(5, 0))
        
        # The Printery Pink sample
        pink_sample = tk.Label(
            colors_frame,
            text="  The Printery Pink  ",
            bg=current_theme.colors.get('printery_pink', '#FFB6C1'),
            fg='black',
            font=('Arial', 10, 'bold')
        )
        pink_sample.pack(side=tk.LEFT, padx=(0, 10))
        
        # The Printery Teal sample
        teal_sample = tk.Label(
            colors_frame,
            text="  The Printery Teal  ",
            bg=current_theme.colors.get('printery_teal', '#20B2AA'),
            fg='white',
            font=('Arial', 10, 'bold')
        )
        teal_sample.pack(side=tk.LEFT)
        
        # Theme status
        theme_status = "✅ The Printery theme loaded successfully!" if current_theme.name == 'printery_theme' else "❌ Default theme not set to The Printery theme"
        status_label = tk.Label(
            theme_frame,
            text=theme_status,
            font=('Arial', 10),
            bg='white',
            fg='green' if '✅' in theme_status else 'red'
        )
        status_label.pack(anchor=tk.W, pady=(5, 0))
    
    def setup_mcp_test(self, parent):
        """Set up MCP testing section."""
        mcp_frame = tk.LabelFrame(
            parent,
            text="MCP Integration Test",
            font=('Arial', 12, 'bold'),
            bg='white',
            padx=10,
            pady=10
        )
        mcp_frame.pack(fill=tk.X, pady=(0, 10))
        
        # MCP requirements check
        requirements = validate_mcp_server_requirements()
        
        req_frame = tk.Frame(mcp_frame, bg='white')
        req_frame.pack(fill=tk.X)
        
        tk.Label(
            req_frame,
            text="MCP Requirements:",
            font=('Arial', 10, 'bold'),
            bg='white'
        ).pack(anchor=tk.W)
        
        for req, status in requirements.items():
            status_text = "✅" if status else "❌"
            req_label = tk.Label(
                req_frame,
                text=f"  {status_text} {req}: {'Available' if status else 'Missing'}",
                font=('Arial', 9),
                bg='white',
                fg='green' if status else 'red'
            )
            req_label.pack(anchor=tk.W)
        
        # Server count
        server_count = len(self.mcp_manager.servers)
        server_label = tk.Label(
            mcp_frame,
            text=f"Configured MCP Servers: {server_count}",
            font=('Arial', 10),
            bg='white'
        )
        server_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Show setup instructions button
        setup_btn = tk.Button(
            mcp_frame,
            text="Show MCP Setup Instructions",
            command=self.show_mcp_instructions,
            bg='#FFB6C1',  # The Printery pink
            fg='black',
            cursor='hand2'
        )
        setup_btn.pack(anchor=tk.W, pady=(5, 0))
    
    def show_mcp_instructions(self):
        """Show MCP setup instructions."""
        instructions = get_mcp_setup_instructions()
        
        # Create instructions window
        inst_window = tk.Toplevel(self.root)
        inst_window.title("MCP Setup Instructions")
        inst_window.geometry("600x500")
        
        text_widget = tk.Text(
            inst_window,
            wrap=tk.WORD,
            font=('Arial', 10),
            padx=10,
            pady=10
        )
        text_widget.pack(fill=tk.BOTH, expand=True)
        
        text_widget.insert('1.0', instructions)
        text_widget.config(state='disabled')
        
        # Close button
        close_btn = tk.Button(
            inst_window,
            text="Close",
            command=inst_window.destroy,
            bg='#20B2AA',
            fg='white',
            cursor='hand2'
        )
        close_btn.pack(pady=10)
    
    def launch_modern_chat(self):
        """Launch the modern chat interface."""
        try:
            # Create a mock assistant object
            class MockAssistant:
                async def process_text_input(self, text):
                    return f"Mock response to: {text}"
                
                async def process_document(self, file_path):
                    return f"Mock document processing for: {file_path}"
            
            # Create a mock config
            mock_config = {}
            
            # Launch the modern chat window
            chat_window = ModernChatWindow(self.root, MockAssistant(), mock_config)
            
            # Show success message
            tk.messagebox.showinfo(
                "Success",
                "The Printery AI Assistant launched successfully!\n\n" +
                "Test the keyboard shortcuts:\n" +
                "• Ctrl+N: New conversation\n" +
                "• Ctrl+Enter: Send message\n" +
                "• Escape: Focus input\n" +
                "• Ctrl+/: Show help"
            )
            
        except Exception as e:
            tk.messagebox.showerror(
                "Error",
                f"Failed to launch chat interface:\n{str(e)}"
            )
    
    def run(self):
        """Run the test application."""
        self.root.mainloop()

if __name__ == "__main__":
    # Import messagebox for the launch function
    import tkinter.messagebox
    
    print("🍒 The Printery AI Assistant - Phase 2 Implementation Test")
    print("=" * 60)
    print("Testing:")
    print("✓ The Printery branding and theme colors")
    print("✓ Keyboard shortcuts implementation")
    print("✓ MCP integration setup")
    print("✓ Modern chat interface")
    print("=" * 60)
    
    app = Phase2TestApp()
    app.run()
