#!/usr/bin/env python3
"""
Simple test for the tabbed settings interface.
Tests only the UI components without heavy dependencies.
"""
import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class MockThemeManager:
    """Mock theme manager for testing."""
    
    def __init__(self):
        self.current_theme = MockTheme()
    
    def get_current_theme(self):
        return self.current_theme
    
    def get_theme_names(self):
        return ["The Printery Theme", "Dark Printery Theme", "Cherry Classic", "Light Theme"]
    
    def get_all_themes(self):
        return [self.current_theme]
    
    def set_current_theme(self, theme_name):
        pass

class MockTheme:
    """Mock theme for testing."""
    
    def __init__(self):
        self.name = "printery_theme"
        self.display_name = "The Printery Theme"
        self.description = "Pink, teal, and white colors matching The Printery logo"
        self.is_dark = False
        self.colors = {
            'bg_primary': '#ffffff',
            'bg_secondary': '#fef7f7',
            'bg_tertiary': '#f0f8ff',
            'text_primary': '#000000',
            'text_secondary': '#4a4a4a',
            'accent': '#20B2AA',
            'accent_hover': '#1a9999',
            'success': '#28a745',
            'warning': '#ffc107',
            'danger': '#dc3545',
            'user_msg': '#FFB6C1',
            'assistant_msg': '#E0F8F8',
            'pink_accent': '#FFB6C1',
            'teal_accent': '#20B2AA',
            'blue_accent': '#20B2AA',
            'border': '#e0e0e0',
            'hover': '#f5f5f5',
            'printery_pink': '#FFB6C1',
            'printery_teal': '#20B2AA',
        }

class MockMCPManager:
    """Mock MCP manager for testing."""
    
    def __init__(self):
        self.servers = {
            'filesystem': MockServer('File System Tools', True, True),
            'git': MockServer('Git Tools', True, False),
            'brave_search': MockServer('Web Search', False, False)
        }
    
    def get_server_status(self):
        return {
            'filesystem': {
                'name': 'File System Tools',
                'connected': True,
                'enabled': True,
                'auto_start': True,
                'tools_count': 5,
                'type': 'external'
            },
            'git': {
                'name': 'Git Tools',
                'connected': False,
                'enabled': True,
                'auto_start': False,
                'tools_count': 0,
                'type': 'external'
            },
            'brave_search': {
                'name': 'Web Search',
                'connected': False,
                'enabled': False,
                'auto_start': False,
                'tools_count': 0,
                'type': 'external'
            }
        }

class MockServer:
    """Mock server for testing."""
    
    def __init__(self, display_name, enabled, auto_start):
        self.display_name = display_name
        self.enabled = enabled
        self.auto_start = auto_start
        self.description = f"Mock {display_name} server"

class MockAssistantManager:
    """Mock assistant manager for testing."""
    
    def get_all_assistants(self):
        return [
            MockAssistant("General Assistant", "gpt-3.5-turbo", 0.7, "Helpful for everyday tasks"),
            MockAssistant("Code Assistant", "gpt-4", 0.3, "Expert programming help"),
            MockAssistant("Creative Writer", "claude-3-sonnet", 0.9, "Storytelling and creative content"),
        ]

class MockAssistant:
    """Mock assistant for testing."""
    
    def __init__(self, name, model, temp, desc):
        self.name = name
        self.preferred_model = model
        self.temperature = temp
        self.description = desc

class TabbedSettingsTest:
    """Test application for tabbed settings interface."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Tabbed Settings Interface Test")
        self.root.geometry("600x400")
        
        # Mock managers
        self.theme_manager = MockThemeManager()
        self.mcp_manager = MockMCPManager()
        self.assistant_manager = MockAssistantManager()
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the test UI."""
        # Apply The Printery colors
        theme = self.theme_manager.get_current_theme()
        self.root.configure(bg=theme.colors['bg_primary'])
        
        # Main frame
        main_frame = tk.Frame(self.root, bg=theme.colors['bg_primary'], padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="🍒 The Printery AI Assistant - Tabbed Settings Test",
            font=('Arial', 16, 'bold'),
            bg=theme.colors['bg_primary'],
            fg=theme.colors['text_primary']
        )
        title_label.pack(pady=(0, 20))
        
        # Color samples to show The Printery branding
        colors_frame = tk.Frame(main_frame, bg=theme.colors['bg_primary'])
        colors_frame.pack(pady=10)
        
        pink_sample = tk.Label(
            colors_frame,
            text="  The Printery Pink  ",
            bg=theme.colors['printery_pink'],
            fg='black',
            font=('Arial', 12, 'bold')
        )
        pink_sample.pack(side=tk.LEFT, padx=(0, 10))
        
        teal_sample = tk.Label(
            colors_frame,
            text="  The Printery Teal  ",
            bg=theme.colors['printery_teal'],
            fg='white',
            font=('Arial', 12, 'bold')
        )
        teal_sample.pack(side=tk.LEFT)
        
        # Test button
        test_btn = tk.Button(
            main_frame,
            text="🗂️ Open Tabbed Settings Window",
            font=('Arial', 14, 'bold'),
            bg=theme.colors['accent'],
            fg='white',
            command=self.open_settings,
            cursor='hand2',
            pady=15
        )
        test_btn.pack(pady=20, fill=tk.X)
        
        # Instructions
        instructions = tk.Text(
            main_frame,
            height=8,
            wrap=tk.WORD,
            font=('Arial', 10),
            bg=theme.colors['bg_secondary'],
            fg=theme.colors['text_primary']
        )
        instructions.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        instructions_text = """Tabbed Settings Interface Test:

This test demonstrates the new tabbed settings window design for The Printery AI Assistant.

Features to test:
• Six organized tabs: General, Themes, AI Assistants, MCP Servers, Memory & Context, Advanced
• The Printery branding with pink and teal colors throughout
• Professional layout with proper spacing and organization
• Modal window behavior
• Settings persistence and management

Click the button above to open the tabbed settings window and explore all the organized settings categories."""
        
        instructions.insert('1.0', instructions_text)
        instructions.config(state='disabled')
    
    def open_settings(self):
        """Open the tabbed settings window."""
        try:
            # Create a simplified settings window for testing
            settings_window = tk.Toplevel(self.root)
            settings_window.title("The Printery AI Assistant - Settings")
            settings_window.geometry("800x600")
            settings_window.transient(self.root)
            settings_window.grab_set()
            
            # Center the window
            settings_window.update_idletasks()
            x = (self.root.winfo_x() + (self.root.winfo_width() // 2) - 
                 (settings_window.winfo_width() // 2))
            y = (self.root.winfo_y() + (self.root.winfo_height() // 2) - 
                 (settings_window.winfo_height() // 2))
            settings_window.geometry(f"+{x}+{y}")
            
            theme = self.theme_manager.get_current_theme()
            settings_window.configure(bg=theme.colors['bg_primary'])
            
            # Main container
            main_frame = tk.Frame(settings_window, bg=theme.colors['bg_primary'])
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # Header
            header_label = tk.Label(
                main_frame,
                text="🍒 The Printery AI Assistant Settings",
                font=('Arial', 16, 'bold'),
                bg=theme.colors['bg_primary'],
                fg=theme.colors['text_primary']
            )
            header_label.pack(pady=(0, 10))
            
            # Create notebook for tabs
            notebook = ttk.Notebook(main_frame)
            notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
            
            # Create sample tabs
            self.create_sample_tabs(notebook, theme)
            
            # Bottom buttons
            buttons_frame = tk.Frame(main_frame, bg=theme.colors['bg_primary'])
            buttons_frame.pack(fill=tk.X, pady=(10, 0))
            
            ok_btn = tk.Button(
                buttons_frame,
                text="OK",
                command=settings_window.destroy,
                bg=theme.colors['success'],
                fg='white',
                cursor='hand2',
                width=12
            )
            ok_btn.pack(side=tk.RIGHT, padx=(10, 0))
            
            apply_btn = tk.Button(
                buttons_frame,
                text="Apply",
                command=lambda: messagebox.showinfo("Apply", "Settings would be applied here"),
                bg=theme.colors['accent'],
                fg='white',
                cursor='hand2',
                width=12
            )
            apply_btn.pack(side=tk.RIGHT, padx=(10, 0))
            
            cancel_btn = tk.Button(
                buttons_frame,
                text="Cancel",
                command=settings_window.destroy,
                bg=theme.colors['bg_tertiary'],
                fg=theme.colors['text_primary'],
                cursor='hand2',
                width=12
            )
            cancel_btn.pack(side=tk.RIGHT)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open settings:\n{str(e)}")
    
    def create_sample_tabs(self, notebook, theme):
        """Create sample tabs to demonstrate the interface."""
        
        # General Tab
        general_frame = ttk.Frame(notebook)
        notebook.add(general_frame, text="General")
        
        general_content = tk.Frame(general_frame, bg=theme.colors['bg_primary'])
        general_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        tk.Label(
            general_content,
            text="Application Settings",
            font=('Arial', 12, 'bold'),
            bg=theme.colors['bg_primary'],
            fg=theme.colors['text_primary']
        ).pack(anchor=tk.W, pady=(0, 10))
        
        auto_save_var = tk.BooleanVar(value=True)
        tk.Checkbutton(
            general_content,
            text="Auto-save conversations",
            variable=auto_save_var,
            bg=theme.colors['bg_primary'],
            fg=theme.colors['text_primary']
        ).pack(anchor=tk.W)
        
        # Themes Tab
        themes_frame = ttk.Frame(notebook)
        notebook.add(themes_frame, text="Themes")
        
        themes_content = tk.Frame(themes_frame, bg=theme.colors['bg_primary'])
        themes_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        tk.Label(
            themes_content,
            text="Current Theme: The Printery Theme",
            font=('Arial', 12, 'bold'),
            bg=theme.colors['bg_primary'],
            fg=theme.colors['text_primary']
        ).pack(anchor=tk.W, pady=(0, 10))
        
        # Color preview
        colors_frame = tk.Frame(themes_content, bg=theme.colors['bg_primary'])
        colors_frame.pack(anchor=tk.W, pady=10)
        
        pink_sample = tk.Label(
            colors_frame,
            text="  The Printery Pink  ",
            bg=theme.colors['printery_pink'],
            fg='black',
            font=('Arial', 10, 'bold')
        )
        pink_sample.pack(side=tk.LEFT, padx=(0, 10))
        
        teal_sample = tk.Label(
            colors_frame,
            text="  The Printery Teal  ",
            bg=theme.colors['printery_teal'],
            fg='white',
            font=('Arial', 10, 'bold')
        )
        teal_sample.pack(side=tk.LEFT)
        
        # AI Assistants Tab
        assistants_frame = ttk.Frame(notebook)
        notebook.add(assistants_frame, text="AI Assistants")
        
        assistants_content = tk.Frame(assistants_frame, bg=theme.colors['bg_primary'])
        assistants_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        tk.Label(
            assistants_content,
            text="Available AI Assistants",
            font=('Arial', 12, 'bold'),
            bg=theme.colors['bg_primary'],
            fg=theme.colors['text_primary']
        ).pack(anchor=tk.W, pady=(0, 10))
        
        # Sample assistant list
        assistants_list = tk.Listbox(
            assistants_content,
            height=6,
            bg=theme.colors['bg_secondary'],
            fg=theme.colors['text_primary']
        )
        assistants_list.pack(fill=tk.X, pady=(0, 10))
        
        sample_assistants = [
            "General Assistant - Helpful for everyday tasks",
            "Code Assistant - Expert programming help", 
            "Creative Writer - Storytelling and creative content"
        ]
        
        for assistant in sample_assistants:
            assistants_list.insert(tk.END, assistant)
        
        # MCP Servers Tab
        mcp_frame = ttk.Frame(notebook)
        notebook.add(mcp_frame, text="MCP Servers")
        
        mcp_content = tk.Frame(mcp_frame, bg=theme.colors['bg_primary'])
        mcp_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        tk.Label(
            mcp_content,
            text="MCP Server Status",
            font=('Arial', 12, 'bold'),
            bg=theme.colors['bg_primary'],
            fg=theme.colors['text_primary']
        ).pack(anchor=tk.W, pady=(0, 10))
        
        status_text = """✅ File System Tools: Connected
❌ Git Tools: Disconnected  
❌ Web Search: Disabled (requires API key)"""
        
        tk.Label(
            mcp_content,
            text=status_text,
            font=('Arial', 10),
            bg=theme.colors['bg_primary'],
            fg=theme.colors['text_primary'],
            justify=tk.LEFT
        ).pack(anchor=tk.W)
        
        # Memory & Context Tab
        memory_frame = ttk.Frame(notebook)
        notebook.add(memory_frame, text="Memory & Context")
        
        memory_content = tk.Frame(memory_frame, bg=theme.colors['bg_primary'])
        memory_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        tk.Label(
            memory_content,
            text="Context Management",
            font=('Arial', 12, 'bold'),
            bg=theme.colors['bg_primary'],
            fg=theme.colors['text_primary']
        ).pack(anchor=tk.W, pady=(0, 10))
        
        context_frame = tk.Frame(memory_content, bg=theme.colors['bg_primary'])
        context_frame.pack(anchor=tk.W, pady=5)
        
        tk.Label(
            context_frame,
            text="Max Context Length:",
            bg=theme.colors['bg_primary'],
            fg=theme.colors['text_primary']
        ).pack(side=tk.LEFT)
        
        context_entry = tk.Entry(
            context_frame,
            width=10,
            bg=theme.colors['bg_secondary'],
            fg=theme.colors['text_primary']
        )
        context_entry.insert(0, "4000")
        context_entry.pack(side=tk.LEFT, padx=(10, 5))
        
        tk.Label(
            context_frame,
            text="tokens",
            bg=theme.colors['bg_primary'],
            fg=theme.colors['text_secondary']
        ).pack(side=tk.LEFT)
        
        # Advanced Tab
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text="Advanced")
        
        advanced_content = tk.Frame(advanced_frame, bg=theme.colors['bg_primary'])
        advanced_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        tk.Label(
            advanced_content,
            text="Advanced Settings",
            font=('Arial', 12, 'bold'),
            bg=theme.colors['bg_primary'],
            fg=theme.colors['text_primary']
        ).pack(anchor=tk.W, pady=(0, 10))
        
        debug_var = tk.BooleanVar(value=False)
        tk.Checkbutton(
            advanced_content,
            text="Enable debug mode",
            variable=debug_var,
            bg=theme.colors['bg_primary'],
            fg=theme.colors['text_primary']
        ).pack(anchor=tk.W)
    
    def run(self):
        """Run the test application."""
        self.root.mainloop()

if __name__ == "__main__":
    print("🍒 The Printery AI Assistant - Tabbed Settings Interface Test")
    print("=" * 60)
    print("Testing the new tabbed settings design with:")
    print("✓ Six organized tabs (General, Themes, AI Assistants, MCP, Memory, Advanced)")
    print("✓ The Printery branding with pink and teal colors")
    print("✓ Professional layout and organization")
    print("✓ Modal window behavior")
    print("=" * 60)
    
    app = TabbedSettingsTest()
    app.run()
