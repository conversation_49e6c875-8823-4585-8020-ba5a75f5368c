[DEFAULT]
app_name = VoiceFlow AI Test
version = 0.1.0
log_level = DEBUG

[VOICE]
wake_word = hey assistant
energy_threshold = 4000
pause_threshold = 0.8
tts_rate = 150
tts_volume = 0.9
tts_voice = 0

[OPENAI]
# This is a dummy key for testing only
api_key = sk-test-1234567890abcdef1234567890abcdef1234567890abcdef
model = gpt-3.5-turbo
max_tokens = 100
temperature = 0.7

[ANTHROPIC]
api_key = test_key
model = claude-3-opus-20240229
max_tokens = 100
temperature = 0.7

[GEMINI]
api_key = test_key
model = gemini-pro
max_tokens = 100
temperature = 0.7

[OLLAMA]
base_url = http://localhost:11434
model = mistral
max_tokens = 100
temperature = 0.7
